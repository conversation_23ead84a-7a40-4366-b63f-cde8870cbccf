<!-- Enhanced Facebook-style Post Component -->
<div class="fb-post" data-post-id="<%= post.id %>">
  <!-- Post Header -->
  <div class="fb-post-header">
    <div class="fb-post-avatar">
      <a href="/profile/user/<%= post.userId %>" class="fb-avatar-link" title="View <%= post.userName || 'User' %>'s profile">
        <% if (post.userPhotoURL) { %>
          <img src="<%= post.userPhotoURL %>" alt="<%= post.userName || 'User' %>" class="fb-avatar">
        <% } else { %>
          <div class="fb-avatar fb-avatar-placeholder">
            <i class="bi bi-person-fill"></i>
          </div>
        <% } %>
      </a>
    </div>
    <div class="fb-post-user-info">
      <div class="fb-post-username">
        <a href="/profile/user/<%= post.userId %>" class="text-decoration-none" title="View <%= post.userName || 'User' %>'s profile">
          <%= post.userName || 'Anonymous' %>
        </a>
      </div>
      <div class="fb-post-time">
        <span class="time-ago" data-time="<%= post.createdAt %>">
          <%= new Date(post.createdAt).toLocaleDateString() %>
        </span>
        <% if (post.category) { %>
          · <span class="badge bg-light text-dark ms-1"><%= post.category %></span>
        <% } %>
      </div>
    </div>
    <div class="fb-post-options">
      <i class="bi bi-three-dots"></i>
    </div>
  </div>

  <!-- Post Content -->
  <div class="fb-post-content">
    <% if (post.title || post.description) { %>
      <div class="fb-post-text">
        <% if (post.title) { %>
          <h5><%= post.title %></h5>
        <% } %>
        <% if (post.description) { %>
          <p><%= post.description %></p>
        <% } %>
      </div>
    <% } %>

    <!-- Media Content -->
    <% if (post.fileUrl) { %>
      <%
        // Determine file type
        let isImage = false;
        let isVideo = false;
        let isPdf = false;

        if (post.fileType) {
          isImage = post.fileType.startsWith('image/');
          isVideo = post.fileType.startsWith('video/');
          isPdf = post.fileType === 'application/pdf';
        } else {
          const url = post.fileUrl.toLowerCase();
          isImage = url.includes('.jpg') || url.includes('.jpeg') || url.includes('.png') || url.includes('.gif') || url.includes('.webp') || url.includes('unsplash.com');
          isVideo = url.includes('.mp4') || url.includes('.webm') || url.includes('.ogg');
          isPdf = url.includes('.pdf');
        }
      %>

      <% if (isImage) { %>
        <img src="<%= post.fileUrl %>" class="fb-post-image" alt="<%= post.title || 'Post image' %>" loading="lazy">
      <% } else if (isVideo) { %>
        <div class="fb-post-file-container video">
          <i class="bi bi-film"></i>
          <div>Video Content</div>
          <a href="<%= post.fileUrl %>" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
            <i class="bi bi-play-fill"></i> Play Video
          </a>
        </div>
      <% } else if (isPdf) { %>
        <div class="fb-post-file-container pdf">
          <i class="bi bi-file-pdf"></i>
          <div>PDF Document</div>
          <a href="<%= post.fileUrl %>" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
            <i class="bi bi-eye"></i> View PDF
          </a>
        </div>
      <% } else { %>
        <div class="fb-post-file-container file">
          <i class="bi bi-file-earmark"></i>
          <div>Document</div>
          <a href="<%= post.fileUrl %>" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
            <i class="bi bi-download"></i> Download
          </a>
        </div>
      <% } %>
    <% } %>
  </div>

  <!-- Post Stats (Facebook Style) -->
  <div class="fb-post-stats" data-post-id="<%= post.id %>">
    <div class="fb-post-reactions">
      <%
        // Ensure likes array exists and calculate count
        const statsLikesArray = Array.isArray(post.likes) ? post.likes : [];
        const likeCount = post.likeCount || statsLikesArray.length || 0;
        const currentUserId = typeof user !== 'undefined' && user ? user.uid : null;
        const userLiked = currentUserId && statsLikesArray.includes(currentUserId);
      %>
      <% if (likeCount > 0) { %>
        <div class="fb-reaction-summary">
          <div class="fb-reaction-icons">
            <div class="fb-reaction-icon fb-like-icon">👍</div>
          </div>
          <span class="fb-reaction-count">
            <% if (userLiked && likeCount === 1) { %>
              You
            <% } else if (userLiked && likeCount > 1) { %>
              You and <%= likeCount - 1 %> other<%= likeCount > 2 ? 's' : '' %>
            <% } else { %>
              <%= likeCount %>
            <% } %>
          </span>
        </div>
      <% } %>
    </div>
    <div class="fb-post-engagement">
      <%
        // Ensure comments array exists and calculate count
        const commentsArray = Array.isArray(post.comments) ? post.comments : [];
        const commentCount = post.commentCount || commentsArray.length || 0;
        const shareCount = post.shareCount || 0;
      %>
      <% if (commentCount > 0 || shareCount > 0) { %>
        <div class="fb-engagement-stats">
          <% if (commentCount > 0) { %>
            <span class="fb-comment-count"><%= commentCount %> comment<%= commentCount !== 1 ? 's' : '' %></span>
          <% } %>
          <% if (commentCount > 0 && shareCount > 0) { %>
            <span class="fb-stat-separator">·</span>
          <% } %>
          <% if (shareCount > 0) { %>
            <span class="fb-share-count"><%= shareCount %> share<%= shareCount !== 1 ? 's' : '' %></span>
          <% } %>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Post Actions (Facebook Style) -->
  <div class="fb-post-actions">
    <%
      // Check if current user has liked this post
      const actionCurrentUserId = typeof user !== 'undefined' && user ? user.uid : null;
      const actionLikesArray = Array.isArray(post.likes) ? post.likes : [];
      const actionIsLiked = actionCurrentUserId && actionLikesArray.includes(actionCurrentUserId);
    %>
    <button class="fb-action-button fb-like-button <%= actionIsLiked ? 'liked' : '' %>"
            data-post-id="<%= post.id %>"
            onclick="handleFacebookLike('<%= post.id %>')"
            type="button">
      <div class="fb-action-icon">
        <% if (actionIsLiked) { %>
          <svg width="18" height="18" viewBox="0 0 24 24" fill="#1877f2">
            <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558-.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z"/>
          </svg>
        <% } else { %>
          <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558-.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z"/>
          </svg>
        <% } %>
      </div>
      <span class="fb-action-text"><%= actionIsLiked ? 'Like' : 'Like' %></span>
    </button>

    <button class="fb-action-button fb-comment-button"
            data-post-id="<%= post.id %>"
            onclick="focusCommentInput('<%= post.id %>')"
            type="button">
      <div class="fb-action-icon">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.486 2 2 6.486 2 12c0 1.466.315 2.856.879 4.108L2.037 21.2a1 1 0 001.2 1.2l5.092-.842A9.956 9.956 0 0012 22c5.514 0 10-4.486 10-10S17.514 2 12 2z"/>
        </svg>
      </div>
      <span class="fb-action-text">Comment</span>
    </button>

    <button class="fb-action-button fb-share-button"
            data-post-id="<%= post.id %>"
            onclick="handleShare('<%= post.id %>')"
            type="button">
      <div class="fb-action-icon">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
        </svg>
      </div>
      <span class="fb-action-text">Share</span>
    </button>
  </div>

  <!-- Comments Section -->
  <div class="fb-comments-section" id="comments-<%= post.id %>">
    <!-- Existing Comments -->
    <% if (post.comments && post.comments.length > 0) { %>
      <div class="fb-existing-comments">
        <% post.comments.forEach(comment => { %>
          <div class="fb-comment" data-comment-id="<%= comment.id %>">
            <div class="fb-comment-avatar">
              <% if (comment.userPhotoURL) { %>
                <img src="<%= comment.userPhotoURL %>" alt="<%= comment.userName %>" class="fb-comment-avatar-img">
              <% } else { %>
                <div class="fb-comment-avatar-placeholder">
                  <i class="bi bi-person-fill"></i>
                </div>
              <% } %>
            </div>
            <div class="fb-comment-content">
              <div class="fb-comment-bubble">
                <div class="fb-comment-author">
                  <a href="/profile/user/<%= comment.userId %>" class="fb-comment-author-link">
                    <%= comment.userName %>
                  </a>
                </div>
                <div class="fb-comment-text"><%= comment.text %></div>
              </div>
              <div class="fb-comment-actions">
                <span class="fb-comment-time time-ago" data-time="<%= comment.createdAt %>">
                  <%= new Date(comment.createdAt).toLocaleDateString() %>
                </span>
                <span class="fb-comment-action" onclick="likeComment('<%= comment.id %>')">Like</span>
                <span class="fb-comment-action" onclick="replyToComment('<%= comment.id %>')">Reply</span>
              </div>
            </div>
          </div>
        <% }); %>
      </div>
    <% } %>

    <!-- Comment Input Section -->
    <div class="fb-comment-input-section" id="comment-input-<%= post.id %>">
      <div class="fb-comment-input-container">
        <div class="fb-comment-input-avatar">
          <% if (typeof user !== 'undefined' && user && user.photoURL) { %>
            <img src="<%= user.photoURL %>" alt="<%= user.displayName %>" class="fb-comment-avatar-img">
          <% } else { %>
            <div class="fb-comment-avatar-placeholder">
              <i class="bi bi-person-fill"></i>
            </div>
          <% } %>
        </div>
        <div class="fb-comment-input-wrapper">
          <textarea
            class="fb-comment-input"
            id="comment-text-<%= post.id %>"
            placeholder="Write a comment..."
            rows="1"
            onkeydown="handleCommentKeydown(event, '<%= post.id %>')"
            oninput="autoResizeTextarea(this)"
          ></textarea>
          <div class="fb-comment-input-actions">
            <button type="button" class="fb-comment-emoji-btn" title="Add emoji">
              <i class="bi bi-emoji-smile"></i>
            </button>
            <button type="button" class="fb-comment-photo-btn" title="Add photo">
              <i class="bi bi-camera"></i>
            </button>
            <button
              type="button"
              class="fb-comment-send-btn"
              onclick="submitComment('<%= post.id %>')"
              title="Send comment"
            >
              <i class="bi bi-send"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

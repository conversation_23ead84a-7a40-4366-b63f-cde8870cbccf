import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import ejsLayouts from 'express-ejs-layouts';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import routes
import authRoutes from './routes/auth.js';
import profileRoutes from './routes/profile.js';
import resourcesRoutes from './routes/resources.js';
import uploadsRoutes from './routes/uploads.js';
import coursesRoutes from './routes/courses.js';
import paymentsRoutes from './routes/payments.js';
import adminRoutes from './routes/admin.js';
import makeAdminRoutes from './routes/makeAdmin.js';
import weatherRoutes from './routes/weatherRoutes.js';
import networkRoutes from './routes/network.js';
import messagingRoutes from './routes/messaging.js';
import transportRoutes from './routes/transport.js';

import marketTrendsRoutes from './routes/marketTrends.js';

// Import middleware
import { isAuthenticated, attachCurrentUser } from './middleware/auth.js';
import { attachAdminStatus } from './middleware/admin.js';

// Import Firebase configuration and authentication service
import { initializeFirebase } from './config/initFirebase.js';
import { getCurrentUser } from './services/firebaseService.js';

// Initialize Firebase
initializeFirebase().then(status => {
  console.log('Firebase initialization status:', status);
}).catch(error => {
  console.error('Error initializing Firebase:', error);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));
app.use(attachCurrentUser);
app.use(attachAdminStatus);

// Set up EJS and layouts
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(ejsLayouts);
app.set('layout', 'layout');
app.set("layout extractScripts", true);

// Public routes - accessible without authentication
app.use('/auth', authRoutes);

// Protected routes - require authentication
app.use('/network', isAuthenticated, networkRoutes);
app.use('/messaging', isAuthenticated, messagingRoutes);
app.use('/transport', isAuthenticated, transportRoutes);
app.use('/market-trends', isAuthenticated, marketTrendsRoutes);

// Protected routes - require authentication
app.use('/profile', isAuthenticated, profileRoutes);
app.use('/resources', isAuthenticated, resourcesRoutes);
app.use('/courses', isAuthenticated, coursesRoutes);
app.use('/payments', isAuthenticated, paymentsRoutes);
app.use('/uploads', isAuthenticated, uploadsRoutes);
app.use('/weather', isAuthenticated, weatherRoutes);



// Admin routes - require authentication and admin privileges
app.use('/admin', adminRoutes);
app.use('/make-admin', makeAdminRoutes); // Development route only



// Redirect /uploads root to /dashboard for simplicity
app.get('/uploads', isAuthenticated, (req, res) => {
  res.redirect('/dashboard');
});





// PWA Offline page
app.get('/offline.html', (req, res) => {
  res.render('offline', {
    title: 'Offline - Sustainable Farming'
  });
});

// Create sample data route (for testing)
app.get('/create-sample-data', async (req, res) => {
  try {
    const { createSampleData } = await import('./utils/createSampleData.js');
    const result = await createSampleData();
    res.json(result);
  } catch (error) {
    console.error('Error creating sample data:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test user creation (for testing)
app.get('/create-test-user', async (req, res) => {
  try {
    const { registerUser } = await import('./services/firebaseService.js');

    const testUser = {
      email: '<EMAIL>',
      password: 'test123456',
      firstName: 'Test',
      lastName: 'User',
      displayName: 'Test User',
      location: 'Test Location',
      farmName: 'Test Farm'
    };

    const user = await registerUser(testUser);
    res.json({
      success: true,
      message: 'Test user created successfully',
      user: {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName
      }
    });
  } catch (error) {
    console.error('Error creating test user:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint for paginated content
app.get('/api/content', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filter = req.query.filter || 'all';

    console.log('API content request:', { page, limit, filter });

    // Import the firebase service
    const { getUploads } = await import('./services/firebaseService.js');

    // Get paginated uploads
    const result = await getUploads({
      page,
      limit,
      filter
    });

    console.log('API content response:', {
      uploadsCount: result.uploads ? result.uploads.length : 0,
      total: result.total,
      hasMore: result.hasMore
    });

    res.json({
      success: true,
      uploads: result.uploads || [],
      pagination: {
        page: result.page || page,
        limit: result.limit || limit,
        total: result.total || 0,
        hasMore: result.hasMore || false
      }
    });
  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      uploads: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        hasMore: false
      }
    });
  }
});

// Home route
app.get('/', (req, res) => {
  res.render('home', {
    originalUrl: req.originalUrl,
    isAuthenticated: req.isAuthenticated ? req.isAuthenticated : false,
    user: req.user || null
  });
});



// Dashboard route (temporarily public for testing) - Content loaded dynamically via API with server-side fallback
app.get('/dashboard', async (req, res) => {
  console.log('Dashboard route accessed');
  try {
    const user = req.user || { displayName: 'Guest User', uid: 'guest' };

    // Get additional user data and uploads for fallback
    try {
      const { getUserData, getUploads } = await import('./services/firebaseService.js');

      // Get user data (skip if guest)
      let userData = {};
      if (user.uid !== 'guest') {
        userData = await getUserData(user.uid) || {};
      }

      // Get uploads for server-side fallback
      const uploadsResult = await getUploads({ limit: 10 });
      const uploads = uploadsResult.uploads || [];

      console.log('Dashboard rendering with:', {
        userExists: !!user,
        uploadsCount: uploads.length,
        userDataExists: !!userData
      });

      res.render('dashboard', {
        user: user,
        userData: userData,
        uploads: uploads, // Add uploads for fallback
        originalUrl: req.originalUrl
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      res.render('dashboard', {
        user: user,
        userData: {},
        uploads: [], // Empty uploads array for fallback
        error: 'Error loading user data: ' + error.message,
        originalUrl: req.originalUrl
      });
    }
  } catch (error) {
    console.error('Error loading dashboard:', error);
    res.render('error', {
      error: 'Error loading dashboard',
      message: error.message
    });
  }
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});

// Keep the process alive
server.on('error', (error) => {
  console.error('Server error:', error);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Course Setup Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .step-card {
            border-left: 4px solid #ffc107;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .step-number {
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        .checklist-item {
            padding: 0.5rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            transition: background-color 0.2s ease;
        }
        .checklist-item:hover {
            background-color: #f8f9fa;
        }
        .premium-badge {
            background: linear-gradient(135deg, #ffd700, #ffa500);
            color: #333;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-weight: bold;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <div class="mb-3">
                        <i class="bi bi-star-fill text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <h1 class="display-4 fw-bold">Premium Course Setup Guide</h1>
                    <p class="lead text-muted">Complete step-by-step guide to create and display premium courses</p>
                    <span class="premium-badge">
                        <i class="bi bi-gem"></i> Premium Feature
                    </span>
                </div>

                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="bi bi-lightning-fill"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-success w-100" onclick="loginAsAdmin()">
                                    <i class="bi bi-box-arrow-in-right"></i> Login as Admin
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-warning w-100" onclick="makeAdmin()">
                                    <i class="bi bi-shield-plus"></i> Make Admin
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-primary w-100" onclick="createTestCourse()">
                                    <i class="bi bi-star"></i> Create Test Course
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-info w-100" onclick="checkCourses()">
                                    <i class="bi bi-search"></i> Check Courses
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step-by-Step Guide -->
                <div class="row">
                    <!-- Step 1 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number">1</div>
                                    <h5 class="mb-0">Admin Setup</h5>
                                </div>
                                <div class="checklist">
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Login to your account</span>
                                        <a href="/simple-login.html" class="btn btn-sm btn-outline-primary ms-2" target="_blank">Login</a>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Make yourself admin</span>
                                        <a href="/make-me-admin" class="btn btn-sm btn-outline-warning ms-2" target="_blank">Make Admin</a>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Access admin panel</span>
                                        <a href="/admin" class="btn btn-sm btn-outline-danger ms-2" target="_blank">Admin Panel</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number">2</div>
                                    <h5 class="mb-0">Create Premium Course</h5>
                                </div>
                                <div class="checklist">
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Go to course management</span>
                                        <a href="/admin/courses" class="btn btn-sm btn-outline-primary ms-2" target="_blank">Courses</a>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Click "Create New Course"</span>
                                        <a href="/admin/courses/new" class="btn btn-sm btn-outline-success ms-2" target="_blank">New Course</a>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Fill basic course information</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number">3</div>
                                    <h5 class="mb-0">Configure Premium Settings</h5>
                                </div>
                                <div class="checklist">
                                    <div class="checklist-item">
                                        <i class="bi bi-check-square-fill text-success"></i>
                                        <span class="ms-2"><strong>Check "Premium Course"</strong></span>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-check-square-fill text-success"></i>
                                        <span class="ms-2"><strong>Set price (e.g., $99.99)</strong></span>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-check-square-fill text-success"></i>
                                        <span class="ms-2"><strong>Choose currency</strong></span>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Set enrollment limit (optional)</span>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Enable admin approval</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number">4</div>
                                    <h5 class="mb-0">Publish Course</h5>
                                </div>
                                <div class="checklist">
                                    <div class="checklist-item">
                                        <i class="bi bi-exclamation-triangle-fill text-warning"></i>
                                        <span class="ms-2"><strong>IMPORTANT: Check "Publish course immediately"</strong></span>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Click "Create Course"</span>
                                    </div>
                                    <div class="checklist-item">
                                        <i class="bi bi-square text-muted"></i>
                                        <span class="ms-2">Add course modules (optional)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Common Issues -->
                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="bi bi-exclamation-triangle-fill"></i> Common Issues & Solutions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Course not showing as premium</h6>
                                <ul class="list-unstyled">
                                    <li>• Make sure "Premium Course" checkbox is checked</li>
                                    <li>• Verify price is set and greater than 0</li>
                                    <li>• Ensure course is published</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Course not visible to users</h6>
                                <ul class="list-unstyled">
                                    <li>• Check "Publish course immediately" is enabled</li>
                                    <li>• Verify course appears in admin course list</li>
                                    <li>• Refresh the courses page</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-clipboard-data"></i> Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" class="alert alert-light">
                            Click "Check Courses" to see current courses and their premium status.
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-link-45deg"></i> Quick Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="/courses" class="btn btn-outline-primary w-100" target="_blank">
                                    <i class="bi bi-mortarboard"></i> View Courses (User)
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/admin/courses" class="btn btn-outline-danger w-100" target="_blank">
                                    <i class="bi bi-gear"></i> Manage Courses (Admin)
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/courses/debug" class="btn btn-outline-secondary w-100" target="_blank">
                                    <i class="bi bi-bug"></i> Debug API
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/test-premium-course.html" class="btn btn-outline-warning w-100" target="_blank">
                                    <i class="bi bi-tools"></i> Advanced Testing
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getAuth, 
            signInWithEmailAndPassword,
            onAuthStateChanged
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
            authDomain: "sustainablefarming-bf265.firebaseapp.com",
            projectId: "sustainablefarming-bf265",
            storageBucket: "sustainablefarming-bf265.appspot.com",
            messagingSenderId: "89904373415",
            appId: "1:89904373415:web:2b8bbc14c7802554cac582"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        const testResults = document.getElementById('testResults');

        function updateTestResults(message, type = 'light') {
            testResults.innerHTML = message;
            testResults.className = `alert alert-${type}`;
        }

        // Global functions
        window.loginAsAdmin = async function() {
            try {
                const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'test123456');
                updateTestResults('✅ Login successful! You can now access admin features.', 'success');
            } catch (error) {
                updateTestResults('❌ Login failed: ' + error.message, 'danger');
            }
        };

        window.makeAdmin = async function() {
            try {
                const response = await fetch('/make-me-admin');
                const result = await response.json();
                
                if (result.success) {
                    updateTestResults('✅ Admin privileges granted! You can now create premium courses.', 'success');
                } else {
                    updateTestResults('❌ Failed to grant admin privileges: ' + result.message, 'danger');
                }
            } catch (error) {
                updateTestResults('❌ Error granting admin privileges: ' + error.message, 'danger');
            }
        };

        window.createTestCourse = async function() {
            try {
                const response = await fetch('/debug-create-premium-course', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateTestResults(`✅ Test premium course created successfully!<br>
                        <strong>Course ID:</strong> ${result.course.id}<br>
                        <strong>Title:</strong> ${result.course.title}<br>
                        <strong>Price:</strong> $${result.course.price} ${result.course.currency}<br>
                        <strong>Premium:</strong> ${result.course.isPremium ? 'Yes' : 'No'}<br>
                        <strong>Published:</strong> ${result.course.isPublished ? 'Yes' : 'No'}`, 'success');
                } else {
                    updateTestResults('❌ Failed to create test course: ' + result.error, 'danger');
                }
            } catch (error) {
                updateTestResults('❌ Error creating test course: ' + error.message, 'danger');
            }
        };

        window.checkCourses = async function() {
            try {
                const response = await fetch('/courses/debug');
                const result = await response.json();
                
                if (response.ok) {
                    const premiumCourses = result.courses.filter(c => c.isPremium);
                    const publishedCourses = result.courses.filter(c => c.isPublished);
                    const publishedPremiumCourses = result.courses.filter(c => c.isPremium && c.isPublished);
                    
                    let html = `
                        <h6>📊 Course Statistics:</h6>
                        <div class="row mb-3">
                            <div class="col-md-3"><strong>Total:</strong> ${result.count}</div>
                            <div class="col-md-3"><strong>Premium:</strong> ${premiumCourses.length}</div>
                            <div class="col-md-3"><strong>Published:</strong> ${publishedCourses.length}</div>
                            <div class="col-md-3"><strong>Published Premium:</strong> ${publishedPremiumCourses.length}</div>
                        </div>
                    `;
                    
                    if (result.courses.length > 0) {
                        html += '<h6>📋 Course List:</h6><div class="row">';
                        result.courses.forEach(course => {
                            const premiumBadge = course.isPremium ? 
                                `<span class="badge bg-warning text-dark">Premium $${course.price}</span>` : 
                                '<span class="badge bg-success">Free</span>';
                            const publishedBadge = course.isPublished ? 
                                '<span class="badge bg-success">Published</span>' : 
                                '<span class="badge bg-secondary">Draft</span>';
                            
                            html += `
                                <div class="col-md-6 mb-2">
                                    <div class="card">
                                        <div class="card-body p-2">
                                            <h6 class="card-title mb-1">${course.title}</h6>
                                            <div class="d-flex gap-1 mb-1">
                                                ${premiumBadge}
                                                ${publishedBadge}
                                            </div>
                                            <small class="text-muted">${course.category || 'No category'}</small>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }
                    
                    updateTestResults(html, 'info');
                } else {
                    updateTestResults('❌ Failed to fetch courses: ' + result.error, 'danger');
                }
            } catch (error) {
                updateTestResults('❌ Error fetching courses: ' + error.message, 'danger');
            }
        };

        // Initialize
        setTimeout(() => {
            updateTestResults('🚀 Premium Course Guide loaded. Follow the steps above to create premium courses.', 'info');
        }, 1000);
    </script>
</body>
</html>

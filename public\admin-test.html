<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-section {
            border-left: 4px solid #28a745;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .test-section:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-working { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="display-4 fw-bold text-success">🛠️ Admin Functionality Test</h1>
                    <p class="lead text-muted">Test and debug admin image upload and delete functionality</p>
                </div>

                <!-- Status Dashboard -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-speedometer2"></i> System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <div id="authStatus" class="d-flex align-items-center">
                                    <span class="status-indicator status-warning"></span>
                                    <span>Authentication: Checking...</span>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div id="adminStatus" class="d-flex align-items-center">
                                    <span class="status-indicator status-warning"></span>
                                    <span>Admin Access: Checking...</span>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div id="uploadStatus" class="d-flex align-items-center">
                                    <span class="status-indicator status-info"></span>
                                    <span>Upload: Ready</span>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div id="deleteStatus" class="d-flex align-items-center">
                                    <span class="status-indicator status-info"></span>
                                    <span>Delete: Ready</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                        
                        <!-- User Status -->
                        <div class="mb-4">
                            <h5><i class="bi bi-person-check"></i> Current User Status</h5>
                            <div id="userStatus" class="alert alert-secondary">
                                Checking authentication...
                            </div>
                        </div>
                        
                        <!-- Admin Actions -->
                        <div class="mb-4">
                            <h5><i class="bi bi-gear"></i> Admin Actions</h5>
                            <div class="d-grid gap-2">
                                <button id="loginBtn" class="btn btn-success" onclick="loginAsAdmin()">
                                    <i class="bi bi-box-arrow-in-right"></i> Login as Test User
                                </button>
                                <button id="makeAdminBtn" class="btn btn-warning" onclick="makeAdmin()">
                                    <i class="bi bi-shield-plus"></i> Make Current User Admin
                                </button>
                                <button id="checkAdminBtn" class="btn btn-info" onclick="checkAdminStatus()">
                                    <i class="bi bi-shield-check"></i> Check Admin Status
                                </button>
                                <a href="/admin" class="btn btn-primary">
                                    <i class="bi bi-speedometer2"></i> Go to Admin Dashboard
                                </a>
                            </div>
                        </div>
                        
                        <!-- Test Results -->
                        <div class="mb-4">
                            <h5><i class="bi bi-clipboard-data"></i> Test Results</h5>
                            <div id="testResults" class="alert alert-light">
                                No tests run yet.
                            </div>
                        </div>
                        
                        <!-- Quick Links -->
                        <div class="mt-4">
                            <h6>Quick Links:</h6>
                            <a href="/simple-login.html" class="btn btn-outline-primary btn-sm me-2">Simple Login</a>
                            <a href="/dashboard" class="btn btn-outline-success btn-sm me-2">Dashboard</a>
                            <a href="/admin" class="btn btn-outline-danger btn-sm">Admin Panel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getAuth, 
            signInWithEmailAndPassword,
            onAuthStateChanged
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
            authDomain: "sustainablefarming-bf265.firebaseapp.com",
            projectId: "sustainablefarming-bf265",
            storageBucket: "sustainablefarming-bf265.appspot.com",
            messagingSenderId: "89904373415",
            appId: "1:89904373415:web:2b8bbc14c7802554cac582"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        const status = document.getElementById('status');
        const userStatus = document.getElementById('userStatus');
        const testResults = document.getElementById('testResults');

        function updateStatus(message, type = 'info') {
            status.innerHTML = `<i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}`;
            status.className = `alert alert-${type}`;
        }

        function updateUserStatus(message, type = 'secondary') {
            userStatus.innerHTML = message;
            userStatus.className = `alert alert-${type}`;
        }

        function updateTestResults(message, type = 'light') {
            testResults.innerHTML = message;
            testResults.className = `alert alert-${type}`;
        }

        // Auth state observer
        onAuthStateChanged(auth, (user) => {
            if (user) {
                updateUserStatus(`
                    <strong>✅ Logged In</strong><br>
                    <strong>Email:</strong> ${user.email}<br>
                    <strong>UID:</strong> ${user.uid}<br>
                    <strong>Display Name:</strong> ${user.displayName || 'Not set'}
                `, 'success');
                updateStatus('User is authenticated. Ready for admin tests.', 'success');
            } else {
                updateUserStatus('❌ Not logged in', 'warning');
                updateStatus('Please log in first to test admin functionality.', 'warning');
            }
        });

        // Global functions
        window.loginAsAdmin = async function() {
            try {
                updateStatus('Logging in as test user...', 'info');
                
                const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'test123456');
                updateStatus('Login successful!', 'success');
                updateTestResults('✅ Login test passed', 'success');
            } catch (error) {
                console.error('Login error:', error);
                updateStatus(`Login failed: ${error.message}`, 'danger');
                updateTestResults('❌ Login test failed: ' + error.message, 'danger');
            }
        };

        window.makeAdmin = async function() {
            try {
                updateStatus('Making current user admin...', 'info');
                
                const response = await fetch('/make-me-admin');
                const result = await response.json();
                
                if (result.success) {
                    updateStatus('Successfully made admin!', 'success');
                    updateTestResults('✅ Admin creation test passed: ' + result.message, 'success');
                } else {
                    updateStatus('Failed to make admin: ' + result.message, 'danger');
                    updateTestResults('❌ Admin creation test failed: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('Make admin error:', error);
                updateStatus('Error making admin: ' + error.message, 'danger');
                updateTestResults('❌ Admin creation test failed: ' + error.message, 'danger');
            }
        };

        window.checkAdminStatus = async function() {
            try {
                updateStatus('Checking admin status...', 'info');
                
                const response = await fetch('/admin');
                
                if (response.ok) {
                    updateStatus('Admin access granted!', 'success');
                    updateTestResults('✅ Admin access test passed - You have admin privileges', 'success');
                } else if (response.status === 403) {
                    updateStatus('Admin access denied - Not an admin', 'warning');
                    updateTestResults('❌ Admin access test failed - No admin privileges', 'warning');
                } else if (response.status === 401) {
                    updateStatus('Admin access denied - Not logged in', 'warning');
                    updateTestResults('❌ Admin access test failed - Not authenticated', 'warning');
                } else {
                    updateStatus('Admin access test failed with status: ' + response.status, 'danger');
                    updateTestResults('❌ Admin access test failed - Server error', 'danger');
                }
            } catch (error) {
                console.error('Check admin error:', error);
                updateStatus('Error checking admin status: ' + error.message, 'danger');
                updateTestResults('❌ Admin status check failed: ' + error.message, 'danger');
            }
        };

        // Initialize
        setTimeout(() => {
            updateStatus('Firebase initialized. Ready for testing.', 'success');
        }, 1000);
    </script>
</body>
</html>

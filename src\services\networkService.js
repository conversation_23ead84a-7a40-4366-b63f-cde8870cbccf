import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  addDoc,
  onSnapshot,
  arrayUnion,
  arrayRemove,
  increment,
  startAfter
} from 'firebase/firestore';
import { db, storage } from '../config/initFirebase.js';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { getCurrentUser, getUserData } from './firebaseService.js';
import { safeFirestoreOperation } from '../utils/errorHandler.js';

// Collection names
const USERS_COLLECTION = 'users';
const CONNECTIONS_COLLECTION = 'connections';
const POSTS_COLLECTION = 'posts';
const COMMENTS_COLLECTION = 'comments';
const MESSAGES_COLLECTION = 'messages';
const CONVERSATIONS_COLLECTION = 'conversations';

// Connection status constants
export const CONNECTION_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected'
};

// Post types
export const POST_TYPE = {
  TEXT: 'text',
  IMAGE: 'image',
  ARTICLE: 'article',
  LINK: 'link'
};

// ==================== PROFILE FUNCTIONS ====================

/**
 * Update a user's professional profile
 */
export const updateUserProfile = async (profileData) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to update your profile');
    }

    const userRef = doc(db, USERS_COLLECTION, user.uid);

    await updateDoc(userRef, {
      ...profileData,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Profile updated successfully'
    };
  } catch (error) {
    console.error('Error updating profile:', error);
    throw error;
  }
};

/**
 * Upload a profile photo
 */
export const uploadProfilePhoto = async (file) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to upload a profile photo');
    }

    // Create a storage reference
    const storageRef = ref(storage, `profile_photos/${user.uid}/${file.name}`);

    // Upload the file
    await uploadBytes(storageRef, file);

    // Get the download URL
    const downloadURL = await getDownloadURL(storageRef);

    // Update the user's profile
    const userRef = doc(db, USERS_COLLECTION, user.uid);
    await updateDoc(userRef, {
      photoURL: downloadURL,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      photoURL: downloadURL
    };
  } catch (error) {
    console.error('Error uploading profile photo:', error);
    throw error;
  }
};

/**
 * Upload a cover image
 */
export const uploadCoverImage = async (file) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to upload a cover image');
    }

    // Create a storage reference
    const storageRef = ref(storage, `cover_images/${user.uid}/${file.name}`);

    // Upload the file
    await uploadBytes(storageRef, file);

    // Get the download URL
    const downloadURL = await getDownloadURL(storageRef);

    // Update the user's profile
    const userRef = doc(db, USERS_COLLECTION, user.uid);
    await updateDoc(userRef, {
      coverImageURL: downloadURL,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      coverImageURL: downloadURL
    };
  } catch (error) {
    console.error('Error uploading cover image:', error);
    throw error;
  }
};

// ==================== CONNECTION FUNCTIONS ====================

/**
 * Send a connection request to another user
 */
export const sendConnectionRequest = async (recipientId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to send a connection request');
    }

    if (user.uid === recipientId) {
      throw new Error('You cannot connect with yourself');
    }

    // Check if a connection already exists
    const existingConnection = await checkConnectionExists(user.uid, recipientId);

    if (existingConnection) {
      throw new Error('A connection request already exists with this user');
    }

    // Create a new connection document
    const connectionRef = doc(collection(db, CONNECTIONS_COLLECTION));
    await setDoc(connectionRef, {
      requesterId: user.uid,
      recipientId: recipientId,
      status: CONNECTION_STATUS.PENDING,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      connectionId: connectionRef.id,
      message: 'Connection request sent successfully'
    };
  } catch (error) {
    console.error('Error sending connection request:', error);
    throw error;
  }
};

/**
 * Accept a connection request
 */
export const acceptConnectionRequest = async (connectionId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to accept a connection request');
    }

    // Get the connection document
    const connectionRef = doc(db, CONNECTIONS_COLLECTION, connectionId);
    const connectionDoc = await getDoc(connectionRef);

    if (!connectionDoc.exists()) {
      throw new Error('Connection request not found');
    }

    const connectionData = connectionDoc.data();

    // Verify that the current user is the recipient
    if (connectionData.recipientId !== user.uid) {
      throw new Error('You are not authorized to accept this connection request');
    }

    // Verify that the status is pending
    if (connectionData.status !== CONNECTION_STATUS.PENDING) {
      throw new Error('This connection request has already been processed');
    }

    // Update the connection status
    await updateDoc(connectionRef, {
      status: CONNECTION_STATUS.ACCEPTED,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Connection request accepted'
    };
  } catch (error) {
    console.error('Error accepting connection request:', error);
    throw error;
  }
};

/**
 * Reject a connection request
 */
export const rejectConnectionRequest = async (connectionId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to reject a connection request');
    }

    // Get the connection document
    const connectionRef = doc(db, CONNECTIONS_COLLECTION, connectionId);
    const connectionDoc = await getDoc(connectionRef);

    if (!connectionDoc.exists()) {
      throw new Error('Connection request not found');
    }

    const connectionData = connectionDoc.data();

    // Verify that the current user is the recipient
    if (connectionData.recipientId !== user.uid) {
      throw new Error('You are not authorized to reject this connection request');
    }

    // Verify that the status is pending
    if (connectionData.status !== CONNECTION_STATUS.PENDING) {
      throw new Error('This connection request has already been processed');
    }

    // Update the connection status
    await updateDoc(connectionRef, {
      status: CONNECTION_STATUS.REJECTED,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Connection request rejected'
    };
  } catch (error) {
    console.error('Error rejecting connection request:', error);
    throw error;
  }
};

/**
 * Remove a connection
 */
export const removeConnection = async (connectionId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to remove a connection');
    }

    // Get the connection document
    const connectionRef = doc(db, CONNECTIONS_COLLECTION, connectionId);
    const connectionDoc = await getDoc(connectionRef);

    if (!connectionDoc.exists()) {
      throw new Error('Connection not found');
    }

    const connectionData = connectionDoc.data();

    // Verify that the current user is either the requester or recipient
    if (connectionData.requesterId !== user.uid && connectionData.recipientId !== user.uid) {
      throw new Error('You are not authorized to remove this connection');
    }

    // Delete the connection
    await deleteDoc(connectionRef);

    return {
      success: true,
      message: 'Connection removed successfully'
    };
  } catch (error) {
    console.error('Error removing connection:', error);
    throw error;
  }
};

/**
 * Check if a connection exists between two users
 */
export const checkConnectionExists = async (userId1, userId2) => {
  try {
    // Check for connection in either direction
    const q1 = query(
      collection(db, CONNECTIONS_COLLECTION),
      where('requesterId', '==', userId1),
      where('recipientId', '==', userId2)
    );

    const q2 = query(
      collection(db, CONNECTIONS_COLLECTION),
      where('requesterId', '==', userId2),
      where('recipientId', '==', userId1)
    );

    const [snapshot1, snapshot2] = await Promise.all([
      getDocs(q1),
      getDocs(q2)
    ]);

    // Combine results
    const connections = [...snapshot1.docs, ...snapshot2.docs];

    if (connections.length === 0) {
      return null;
    }

    // Return the first connection found
    const connection = connections[0];
    return {
      id: connection.id,
      ...connection.data()
    };
  } catch (error) {
    console.error('Error checking connection:', error);
    throw error;
  }
};

/**
 * Get all connections for the current user
 */
export const getUserConnections = async (status = CONNECTION_STATUS.ACCEPTED) => {
  const user = getCurrentUser();

  if (!user) {
    return []; // Return empty array instead of throwing error
  }

  return await safeFirestoreOperation(async () => {
    // Get connections where user is either requester or recipient
    const q1 = query(
      collection(db, CONNECTIONS_COLLECTION),
      where('requesterId', '==', user.uid),
      where('status', '==', status)
    );

    const q2 = query(
      collection(db, CONNECTIONS_COLLECTION),
      where('recipientId', '==', user.uid),
      where('status', '==', status)
    );

    const [snapshot1, snapshot2] = await Promise.all([
      getDocs(q1),
      getDocs(q2)
    ]);

    // Combine results
    const connections = [...snapshot1.docs, ...snapshot2.docs];

    // Get user data for each connection
    const connectionsWithUserData = await Promise.all(
      connections.map(async (connection) => {
        const connectionData = connection.data();
        const otherUserId = connectionData.requesterId === user.uid
          ? connectionData.recipientId
          : connectionData.requesterId;

        const otherUserData = await getUserData(otherUserId);

        return {
          id: connection.id,
          ...connectionData,
          otherUser: {
            uid: otherUserId,
            ...otherUserData
          },
          isRequester: connectionData.requesterId === user.uid
        };
      })
    );

    return connectionsWithUserData;
  }, []); // Return empty array as fallback
};

/**
 * Get pending connection requests for the current user
 */
export const getPendingConnectionRequests = async () => {
  try {
    return await getUserConnections(CONNECTION_STATUS.PENDING);
  } catch (error) {
    console.error('Error getting pending connection requests:', error);
    throw error;
  }
};

/**
 * Enhanced search for farmers by name, location, or farming type
 * This implementation uses a simpler approach that works better with Firestore
 */

// ==================== POST FUNCTIONS ====================

/**
 * Create a new post
 */
export const createPost = async (postData) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to create a post');
    }

    // Create a new post document
    const postRef = doc(collection(db, POSTS_COLLECTION));

    await setDoc(postRef, {
      authorId: user.uid,
      ...postData,
      likes: 0,
      comments: 0,
      shares: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      postId: postRef.id,
      message: 'Post created successfully'
    };
  } catch (error) {
    console.error('Error creating post:', error);
    throw error;
  }
};

/**
 * Upload an image for a post
 */
export const uploadPostImage = async (file) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to upload an image');
    }

    // Create a storage reference
    const fileName = `${Date.now()}_${file.name}`;
    const storageRef = ref(storage, `post_images/${user.uid}/${fileName}`);

    // Upload the file
    await uploadBytes(storageRef, file);

    // Get the download URL
    const downloadURL = await getDownloadURL(storageRef);

    return {
      success: true,
      imageURL: downloadURL
    };
  } catch (error) {
    console.error('Error uploading post image:', error);
    throw error;
  }
};

/**
 * Get posts for the network feed
 */
export const getNetworkFeed = async (limit = 20, lastVisible = null) => {
  const user = getCurrentUser();

  if (!user) {
    return { posts: [], lastVisible: null }; // Return empty data instead of throwing error
  }

  return await safeFirestoreOperation(async () => {

    try {
      // Get all connections for the current user
      const connections = await getUserConnections();

      // Extract the user IDs of connections
      const connectionUserIds = connections.map(connection => connection.otherUser.uid);

      // Include the current user's ID
      const userIds = [user.uid, ...connectionUserIds];

      // Create a query to get posts from the user and their connections
      // Note: Firestore 'in' operator has a limit of 10 items
      // For simplicity, we'll just get all posts and filter them in memory
      let postsQuery;

      try {
        postsQuery = query(
          collection(db, POSTS_COLLECTION),
          orderBy('createdAt', 'desc')
        );

        // Apply pagination if lastVisible is provided
        if (lastVisible) {
          postsQuery = query(
            postsQuery,
            startAfter(lastVisible)
          );
        }
      } catch (error) {
        // If there's an error with the query, use a simpler one
        console.warn('Error creating query with ordering, using simpler query:', error);
        postsQuery = query(collection(db, POSTS_COLLECTION));
      }

      const snapshot = await getDocs(postsQuery);

      // Filter posts by user IDs
      const filteredDocs = snapshot.docs.filter(doc => {
        const postData = doc.data();
        return userIds.includes(postData.authorId);
      });

      // Apply limit after filtering
      const limitedDocs = filteredDocs.slice(0, limit);

      // Get the last visible document for pagination
      const lastVisibleDoc = limitedDocs.length > 0 ? limitedDocs[limitedDocs.length - 1] : null;

      // Get author data for each post
      const postsWithAuthorData = limitedDocs.length > 0 ?
        await Promise.all(
          limitedDocs.map(async (doc) => {
            const postData = doc.data();
            const authorData = await getUserData(postData.authorId);

            return {
              id: doc.id,
              ...postData,
              author: {
                uid: postData.authorId,
                ...authorData
              },
              // Convert timestamps to dates
              createdAt: postData.createdAt ? postData.createdAt.toDate() : null,
              updatedAt: postData.updatedAt ? postData.updatedAt.toDate() : null
            };
          })
        ) : [];

      return {
        posts: postsWithAuthorData,
        lastVisible: lastVisibleDoc
      };
    } catch (indexError) {
      // Check if this is an index error
      if (indexError.message && indexError.message.includes('requires an index')) {
        console.warn('Index for posts query is still being built. Falling back to simpler query.');

        // Fallback to a simpler query without ordering
        // Use a number directly instead of the limit function to avoid errors
        const simpleQuery = query(
          collection(db, POSTS_COLLECTION)
        );

        const snapshot = await getDocs(simpleQuery);

        // Convert to array of post data
        const posts = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // Sort manually by createdAt
        posts.sort((a, b) => {
          const timeA = a.createdAt ? a.createdAt.seconds : 0;
          const timeB = b.createdAt ? b.createdAt.seconds : 0;
          return timeB - timeA; // descending order
        });

        // Get all connections for the current user
        const connections = await getUserConnections();

        // Extract the user IDs of connections
        const connectionUserIds = connections.map(connection => connection.otherUser.uid);

        // Include the current user's ID
        const userIds = [user.uid, ...connectionUserIds];

        // Filter posts by user IDs
        const filteredPosts = posts.filter(post => userIds.includes(post.authorId));

        // Apply limit
        const limitedPosts = filteredPosts.slice(0, limit);

        // Get author data for each post
        const postsWithAuthorData = limitedPosts.length > 0 ?
          await Promise.all(
            limitedPosts.map(async (post) => {
              const authorData = await getUserData(post.authorId);

              return {
                ...post,
                author: {
                  uid: post.authorId,
                  ...authorData
                },
                // Convert timestamps to dates if they exist
                createdAt: post.createdAt ? post.createdAt.toDate() : null,
                updatedAt: post.updatedAt ? post.updatedAt.toDate() : null
              };
            })
          ) : [];

        return {
          posts: postsWithAuthorData,
          lastVisible: null // Can't use pagination with this fallback
        };
      } else {
        // If it's not an index error, rethrow
        throw indexError;
      }
    }
  }, { posts: [], lastVisible: null }); // Return empty data as fallback
};

/**
 * Like a post
 */
export const likePost = async (postId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to like a post');
    }

    // Get the post document
    const postRef = doc(db, POSTS_COLLECTION, postId);
    const postDoc = await getDoc(postRef);

    if (!postDoc.exists()) {
      throw new Error('Post not found');
    }

    // Check if the user has already liked the post
    const likeRef = doc(db, `${POSTS_COLLECTION}/${postId}/likes/${user.uid}`);
    const likeDoc = await getDoc(likeRef);

    if (likeDoc.exists()) {
      // User has already liked the post, so unlike it
      await deleteDoc(likeRef);

      // Decrement the likes count
      await updateDoc(postRef, {
        likes: increment(-1)
      });

      return {
        success: true,
        liked: false,
        message: 'Post unliked successfully'
      };
    } else {
      // User has not liked the post, so like it
      await setDoc(likeRef, {
        userId: user.uid,
        createdAt: serverTimestamp()
      });

      // Increment the likes count
      await updateDoc(postRef, {
        likes: increment(1)
      });

      return {
        success: true,
        liked: true,
        message: 'Post liked successfully'
      };
    }
  } catch (error) {
    console.error('Error liking post:', error);
    throw error;
  }
};

/**
 * Add a comment to a post
 */
export const addComment = async (postId, commentText) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to comment on a post');
    }

    // Get the post document
    const postRef = doc(db, POSTS_COLLECTION, postId);
    const postDoc = await getDoc(postRef);

    if (!postDoc.exists()) {
      throw new Error('Post not found');
    }

    // Create a new comment document
    const commentRef = doc(collection(db, `${POSTS_COLLECTION}/${postId}/${COMMENTS_COLLECTION}`));

    await setDoc(commentRef, {
      postId: postId,
      authorId: user.uid,
      text: commentText,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Increment the comments count
    await updateDoc(postRef, {
      comments: increment(1)
    });

    return {
      success: true,
      commentId: commentRef.id,
      message: 'Comment added successfully'
    };
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

/**
 * Get comments for a post
 */
export const getPostComments = async (postId, limit = 10, lastVisible = null) => {
  try {
    // Get the post document
    const postRef = doc(db, POSTS_COLLECTION, postId);
    const postDoc = await getDoc(postRef);

    if (!postDoc.exists()) {
      throw new Error('Post not found');
    }

    // Create a query to get comments for the post
    let commentsQuery = query(
      collection(db, `${POSTS_COLLECTION}/${postId}/${COMMENTS_COLLECTION}`),
      orderBy('createdAt', 'desc')
    );

    // Apply pagination if lastVisible is provided
    if (lastVisible) {
      commentsQuery = query(
        commentsQuery,
        startAfter(lastVisible),
        limit(limit)
      );
    } else {
      commentsQuery = query(
        commentsQuery,
        limit(limit)
      );
    }

    const snapshot = await getDocs(commentsQuery);

    // Get the last visible document for pagination
    const lastVisibleDoc = snapshot.docs[snapshot.docs.length - 1];

    // Get author data for each comment
    const commentsWithAuthorData = await Promise.all(
      snapshot.docs.map(async (doc) => {
        const commentData = doc.data();
        const authorData = await getUserData(commentData.authorId);

        return {
          id: doc.id,
          ...commentData,
          author: {
            uid: commentData.authorId,
            ...authorData
          },
          // Convert timestamps to dates
          createdAt: commentData.createdAt ? commentData.createdAt.toDate() : null,
          updatedAt: commentData.updatedAt ? commentData.updatedAt.toDate() : null
        };
      })
    );

    return {
      comments: commentsWithAuthorData,
      lastVisible: lastVisibleDoc
    };
  } catch (error) {
    console.error('Error getting post comments:', error);
    throw error;
  }
};

/**
 * Share a post
 */
export const sharePost = async (postId, shareText = '') => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to share a post');
    }

    // Get the original post
    const originalPostRef = doc(db, POSTS_COLLECTION, postId);
    const originalPostDoc = await getDoc(originalPostRef);

    if (!originalPostDoc.exists()) {
      throw new Error('Post not found');
    }

    const originalPostData = originalPostDoc.data();

    // Create a new post document for the share
    const sharePostRef = doc(collection(db, POSTS_COLLECTION));

    await setDoc(sharePostRef, {
      authorId: user.uid,
      type: POST_TYPE.SHARE,
      text: shareText,
      originalPostId: postId,
      originalAuthorId: originalPostData.authorId,
      likes: 0,
      comments: 0,
      shares: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Increment the shares count on the original post
    await updateDoc(originalPostRef, {
      shares: increment(1)
    });

    return {
      success: true,
      shareId: sharePostRef.id,
      message: 'Post shared successfully'
    };
  } catch (error) {
    console.error('Error sharing post:', error);
    throw error;
  }
};

// ==================== SEARCH FUNCTIONS ====================

/**
 * Search for farmers by name, location, or specialization
 */
export const searchFarmers = async (searchTerm, limit = 20) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to search for farmers');
    }

    // Get all users except the current user
    const usersRef = collection(db, USERS_COLLECTION);
    const usersSnapshot = await getDocs(usersRef);

    // Filter users based on search term
    let filteredUsers = usersSnapshot.docs
      .map(doc => ({
        uid: doc.id,
        ...doc.data()
      }))
      .filter(userData => userData.uid !== user.uid); // Exclude current user

    // Apply search filter if a term is provided
    if (searchTerm && searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase().trim();
      filteredUsers = filteredUsers.filter(userData => {
        // Search in display name
        if (userData.displayName && userData.displayName.toLowerCase().includes(term)) {
          return true;
        }

        // Search in location
        if (userData.location && userData.location.toLowerCase().includes(term)) {
          return true;
        }

        // Search in farming type
        if (userData.farmingType && userData.farmingType.toLowerCase().includes(term)) {
          return true;
        }

        // Search in bio
        if (userData.bio && userData.bio.toLowerCase().includes(term)) {
          return true;
        }

        // Search in specializations
        if (userData.specializations && Array.isArray(userData.specializations)) {
          return userData.specializations.some(spec =>
            spec.toLowerCase().includes(term)
          );
        }

        return false;
      });
    }

    // Limit results
    filteredUsers = filteredUsers.slice(0, limit);

    // Check connection status for each user
    const usersWithConnectionStatus = await Promise.all(
      filteredUsers.map(async (userData) => {
        const connection = await checkConnectionExists(user.uid, userData.uid);

        let connectionStatus = 'none';
        let connectionId = null;
        let isRequester = false;

        if (connection) {
          connectionStatus = connection.status;
          connectionId = connection.id;
          isRequester = connection.requesterId === user.uid;
        }

        return {
          ...userData,
          connectionStatus,
          connectionId,
          isRequester
        };
      })
    );

    return usersWithConnectionStatus;
  } catch (error) {
    console.error('Error searching for farmers:', error);
    throw error;
  }
};

/**
 * Get suggested connections for the current user
 */
export const getSuggestedConnections = async (limit = 10) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to get suggested connections');
    }

    // Get the current user's data
    const userData = await getUserData(user.uid);

    // Get all existing connections
    const connections = await getUserConnections();
    const connectionUserIds = connections.map(connection => connection.otherUser.uid);

    // Also include pending connections
    const pendingConnections = await getPendingConnectionRequests();
    const pendingConnectionUserIds = pendingConnections.map(connection => connection.otherUser.uid);

    // Combine all user IDs to exclude
    const excludeUserIds = [user.uid, ...connectionUserIds, ...pendingConnectionUserIds];

    // Create queries based on user's location and specializations
    let queries = [];

    if (userData.location) {
      queries.push(
        query(
          collection(db, USERS_COLLECTION),
          where('location', '==', userData.location),
          limit(limit * 2)
        )
      );
    }

    if (userData.specializations && userData.specializations.length > 0) {
      for (const specialization of userData.specializations) {
        queries.push(
          query(
            collection(db, USERS_COLLECTION),
            where('specializations', 'array-contains', specialization),
            limit(limit * 2)
          )
        );
      }
    }

    // If no specific queries could be created, get random users
    if (queries.length === 0) {
      queries.push(
        query(
          collection(db, USERS_COLLECTION),
          limit(limit * 3)
        )
      );
    }

    // Execute all queries
    const snapshots = await Promise.all(queries.map(q => getDocs(q)));

    // Combine results and remove duplicates and excluded users
    const userDocs = snapshots.flatMap(snapshot => snapshot.docs);
    const uniqueUserIds = new Set();
    const suggestedUsers = [];

    for (const doc of userDocs) {
      if (!uniqueUserIds.has(doc.id) && !excludeUserIds.includes(doc.id)) {
        uniqueUserIds.add(doc.id);
        suggestedUsers.push({
          uid: doc.id,
          ...doc.data()
        });
      }

      // Limit the number of suggestions
      if (suggestedUsers.length >= limit) {
        break;
      }
    }

    return suggestedUsers;
  } catch (error) {
    console.error('Error getting suggested connections:', error);
    throw error;
  }
};

// ==================== MESSAGING FUNCTIONS ====================

/**
 * Get or create a conversation between two users
 */
export const getOrCreateConversation = async (otherUserId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to access conversations');
    }

    // Check if users are connected
    const connection = await checkConnectionExists(user.uid, otherUserId);

    if (!connection || connection.status !== CONNECTION_STATUS.ACCEPTED) {
      throw new Error('You must be connected with this user to start a conversation');
    }

    // Sort user IDs to ensure consistent conversation ID
    const userIds = [user.uid, otherUserId].sort();
    const conversationId = userIds.join('_');

    // Check if conversation exists
    const conversationRef = doc(db, CONVERSATIONS_COLLECTION, conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      // Create a new conversation
      await setDoc(conversationRef, {
        participants: userIds,
        lastMessage: null,
        lastMessageTimestamp: null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }

    // Get the other user's data
    const otherUserData = await getUserData(otherUserId);

    return {
      id: conversationId,
      otherUser: {
        uid: otherUserId,
        ...otherUserData
      }
    };
  } catch (error) {
    console.error('Error getting or creating conversation:', error);
    throw error;
  }
};

/**
 * Send a message in a conversation
 */
export const sendMessage = async (conversationId, messageText) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to send messages');
    }

    // Get the conversation
    const conversationRef = doc(db, CONVERSATIONS_COLLECTION, conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      throw new Error('Conversation not found');
    }

    const conversationData = conversationDoc.data();

    // Verify that the current user is a participant
    if (!conversationData.participants.includes(user.uid)) {
      throw new Error('You are not a participant in this conversation');
    }

    // Create a new message document
    const messageRef = doc(collection(db, `${CONVERSATIONS_COLLECTION}/${conversationId}/${MESSAGES_COLLECTION}`));

    const messageData = {
      conversationId: conversationId,
      senderId: user.uid,
      text: messageText,
      read: false,
      createdAt: serverTimestamp()
    };

    await setDoc(messageRef, messageData);

    // Update the conversation with the last message
    await updateDoc(conversationRef, {
      lastMessage: messageText,
      lastMessageTimestamp: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      messageId: messageRef.id,
      message: messageData
    };
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

/**
 * Get messages for a conversation
 */
export const getConversationMessages = async (conversationId, limit = 20, lastVisible = null) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to view messages');
    }

    // Get the conversation
    const conversationRef = doc(db, CONVERSATIONS_COLLECTION, conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      throw new Error('Conversation not found');
    }

    const conversationData = conversationDoc.data();

    // Verify that the current user is a participant
    if (!conversationData.participants.includes(user.uid)) {
      throw new Error('You are not a participant in this conversation');
    }

    // Create a query to get messages for the conversation
    let messagesQuery = query(
      collection(db, `${CONVERSATIONS_COLLECTION}/${conversationId}/${MESSAGES_COLLECTION}`),
      orderBy('createdAt', 'desc')
    );

    // Apply pagination if lastVisible is provided
    if (lastVisible) {
      messagesQuery = query(
        messagesQuery,
        startAfter(lastVisible),
        limit(limit)
      );
    } else {
      messagesQuery = query(
        messagesQuery,
        limit(limit)
      );
    }

    const snapshot = await getDocs(messagesQuery);

    // Get the last visible document for pagination
    const lastVisibleDoc = snapshot.docs[snapshot.docs.length - 1];

    // Process messages
    const messages = snapshot.docs.map(doc => {
      const messageData = doc.data();

      return {
        id: doc.id,
        ...messageData,
        // Convert timestamp to date
        createdAt: messageData.createdAt ? messageData.createdAt.toDate() : null,
        // Flag for current user's messages
        isCurrentUser: messageData.senderId === user.uid
      };
    });

    return {
      messages,
      lastVisible: lastVisibleDoc
    };
  } catch (error) {
    console.error('Error getting conversation messages:', error);
    throw error;
  }
};

/**
 * Mark messages as read
 */
export const markMessagesAsRead = async (conversationId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to mark messages as read');
    }

    // Get the conversation
    const conversationRef = doc(db, CONVERSATIONS_COLLECTION, conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      throw new Error('Conversation not found');
    }

    const conversationData = conversationDoc.data();

    // Verify that the current user is a participant
    if (!conversationData.participants.includes(user.uid)) {
      throw new Error('You are not a participant in this conversation');
    }

    // Get unread messages sent by the other user
    const messagesQuery = query(
      collection(db, `${CONVERSATIONS_COLLECTION}/${conversationId}/${MESSAGES_COLLECTION}`),
      where('senderId', '!=', user.uid),
      where('read', '==', false)
    );

    const snapshot = await getDocs(messagesQuery);

    // Mark each message as read
    const updatePromises = snapshot.docs.map(doc =>
      updateDoc(doc.ref, { read: true })
    );

    await Promise.all(updatePromises);

    return {
      success: true,
      count: snapshot.docs.length
    };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
};

/**
 * Get all conversations for the current user
 */
export const getUserConversations = async () => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to view conversations');
    }

    // Get conversations where the user is a participant
    const conversationsQuery = query(
      collection(db, CONVERSATIONS_COLLECTION),
      where('participants', 'array-contains', user.uid),
      orderBy('updatedAt', 'desc')
    );

    const snapshot = await getDocs(conversationsQuery);

    // Process conversations
    const conversationsWithUserData = await Promise.all(
      snapshot.docs.map(async (doc) => {
        const conversationData = doc.data();

        // Get the other participant's ID
        const otherUserId = conversationData.participants.find(id => id !== user.uid);

        // Get the other user's data
        const otherUserData = await getUserData(otherUserId);

        // Get unread message count
        const unreadQuery = query(
          collection(db, `${CONVERSATIONS_COLLECTION}/${doc.id}/${MESSAGES_COLLECTION}`),
          where('senderId', '==', otherUserId),
          where('read', '==', false)
        );

        const unreadSnapshot = await getDocs(unreadQuery);

        return {
          id: doc.id,
          ...conversationData,
          otherUser: {
            uid: otherUserId,
            ...otherUserData
          },
          unreadCount: unreadSnapshot.docs.length,
          // Convert timestamps to dates
          lastMessageTimestamp: conversationData.lastMessageTimestamp
            ? conversationData.lastMessageTimestamp.toDate()
            : null,
          createdAt: conversationData.createdAt ? conversationData.createdAt.toDate() : null,
          updatedAt: conversationData.updatedAt ? conversationData.updatedAt.toDate() : null
        };
      })
    );

    return conversationsWithUserData;
  } catch (error) {
    console.error('Error getting user conversations:', error);
    throw error;
  }
};

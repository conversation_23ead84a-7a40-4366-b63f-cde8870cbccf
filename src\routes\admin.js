import express from 'express';
import { isAuthenticated } from '../middleware/auth.js';
import { isAdmin } from '../middleware/admin.js';
import {
  getAllCoursesAdmin,
  createCourse,
  updateCourse,
  deleteCourse,
  getAllModulesAdmin,
  createModule,
  updateModule,
  deleteModule,
  reorderModules,
  toggleCoursePublished,
  toggleModulePublished
} from '../services/adminCourseService.js';
import {
  getAllResourcesAdmin,
  createResource,
  updateResource,
  deleteResource,
  getResourceById,
  toggleResourcePublished
} from '../services/adminResourceService.js';
import { getCourseById, getModuleById } from '../services/courseService.js';

const router = express.Router();

// Admin login page (no auth required)
router.get('/login', (req, res) => {
  // If already logged in and is admin, redirect to admin dashboard
  if (req.user && req.isAdmin) {
    return res.redirect('/admin');
  }

  // If logged in but not admin, redirect to home
  if (req.user) {
    return res.redirect('/');
  }

  res.render('admin/login');
});

// Apply authentication and admin middleware to all other routes
router.use(isAuthenticated, isAdmin);

// Admin dashboard
router.get('/', async (req, res) => {
  try {
    res.render('admin/dashboard', {
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error('Error loading admin dashboard:', error);
    res.render('admin/dashboard', {
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error loading admin dashboard: ' + error.message,
      layout: 'admin-layout'
    });
  }
});

// Premium course enrollments management
router.get('/enrollments', async (req, res) => {
  try {
    res.render('admin/enrollments', {
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error('Error loading enrollments page:', error);
    res.render('admin/enrollments', {
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error loading enrollments: ' + error.message,
      layout: 'admin-layout'
    });
  }
});

// Course management
router.get('/courses', async (req, res) => {
  try {
    const courses = await getAllCoursesAdmin();

    res.render('admin/courses/index', {
      courses,
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error('Error loading admin courses:', error);
    res.render('admin/courses/index', {
      courses: [],
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error loading courses: ' + error.message,
      layout: 'admin-layout'
    });
  }
});

// Create course form
router.get('/courses/new', (req, res) => {
  res.render('admin/courses/new', {
    user: req.user,
    isAdmin: req.isAdmin,
    layout: 'admin-layout'
  });
});

// Create course action
router.post('/courses', async (req, res) => {
  try {
    const courseData = {
      title: req.body.title,
      description: req.body.description,
      category: req.body.category,
      level: req.body.level,
      duration: parseInt(req.body.duration) || 0,
      imageDataUrl: req.body.imageDataUrl,
      isPublished: req.body.isPublished === 'on',
      objectives: req.body.objectives ? req.body.objectives.split('\n').filter(obj => obj.trim() !== '') : [],
      prerequisites: req.body.prerequisites ? req.body.prerequisites.split('\n').filter(pre => pre.trim() !== '') : [],
      tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : []
    };

    const course = await createCourse(courseData);

    res.redirect(`/admin/courses/${course.id}/modules`);
  } catch (error) {
    console.error('Error creating course:', error);
    res.render('admin/courses/new', {
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error creating course: ' + error.message,
      formData: req.body,
      layout: 'admin-layout'
    });
  }
});

// Edit course form
router.get('/courses/:id/edit', async (req, res) => {
  try {
    const course = await getCourseById(req.params.id);

    if (!course) {
      return res.status(404).render('error', {
        error: 'Course not found',
        message: 'The course you are trying to edit does not exist.'
      });
    }

    res.render('admin/courses/edit', {
      course,
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error(`Error loading course ${req.params.id} for editing:`, error);
    res.render('admin/error', {
      error: 'Error loading course',
      message: error.message,
      layout: 'admin-layout'
    });
  }
});

// Update course action
router.post('/courses/:id', async (req, res) => {
  try {
    const courseData = {
      title: req.body.title,
      description: req.body.description,
      category: req.body.category,
      level: req.body.level,
      duration: parseInt(req.body.duration) || 0,
      imageDataUrl: req.body.imageDataUrl,
      isPublished: req.body.isPublished === 'on',
      objectives: req.body.objectives ? req.body.objectives.split('\n').filter(obj => obj.trim() !== '') : [],
      prerequisites: req.body.prerequisites ? req.body.prerequisites.split('\n').filter(pre => pre.trim() !== '') : [],
      tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : []
    };

    await updateCourse(req.params.id, courseData);

    res.redirect('/admin/courses');
  } catch (error) {
    console.error(`Error updating course ${req.params.id}:`, error);

    // Get the course again to display the form with the error
    const course = await getCourseById(req.params.id);

    res.render('admin/courses/edit', {
      course,
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error updating course: ' + error.message,
      formData: req.body,
      layout: 'admin-layout'
    });
  }
});

// Delete course action
router.post('/courses/:id/delete', async (req, res) => {
  try {
    await deleteCourse(req.params.id);

    res.redirect('/admin/courses');
  } catch (error) {
    console.error(`Error deleting course ${req.params.id}:`, error);
    res.redirect('/admin/courses?error=' + encodeURIComponent('Error deleting course: ' + error.message));
  }
});

// Toggle course published status
router.post('/courses/:id/toggle-published', async (req, res) => {
  try {
    const course = await getCourseById(req.params.id);

    if (!course) {
      return res.status(404).json({ success: false, message: 'Course not found' });
    }

    await toggleCoursePublished(req.params.id, !course.isPublished);

    res.json({ success: true, isPublished: !course.isPublished });
  } catch (error) {
    console.error(`Error toggling published status for course ${req.params.id}:`, error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Module management
router.get('/courses/:courseId/modules', async (req, res) => {
  try {
    const course = await getCourseById(req.params.courseId);

    if (!course) {
      return res.status(404).render('admin/error', {
        error: 'Course not found',
        message: 'The course you are looking for does not exist.',
        layout: 'admin-layout'
      });
    }

    const modules = await getAllModulesAdmin(req.params.courseId);

    res.render('admin/modules/index', {
      course,
      modules,
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error(`Error loading modules for course ${req.params.courseId}:`, error);
    res.render('admin/error', {
      error: 'Error loading modules',
      message: error.message,
      layout: 'admin-layout'
    });
  }
});

// Create module form
router.get('/courses/:courseId/modules/new', async (req, res) => {
  try {
    const course = await getCourseById(req.params.courseId);

    if (!course) {
      return res.status(404).render('admin/error', {
        error: 'Course not found',
        message: 'The course you are looking for does not exist.',
        layout: 'admin-layout'
      });
    }

    res.render('admin/modules/new', {
      course,
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error(`Error loading new module form for course ${req.params.courseId}:`, error);
    res.render('admin/error', {
      error: 'Error loading new module form',
      message: error.message,
      layout: 'admin-layout'
    });
  }
});

// Create module action
router.post('/courses/:courseId/modules', async (req, res) => {
  try {
    const moduleData = {
      title: req.body.title,
      description: req.body.description,
      content: req.body.content,
      videoUrl: req.body.videoUrl,
      duration: parseInt(req.body.duration) || 0,
      order: parseInt(req.body.order) || 0,
      isPublished: req.body.isPublished === 'on',
      resources: []
    };

    // Process resources if provided
    if (req.body.resourceNames && Array.isArray(req.body.resourceNames)) {
      for (let i = 0; i < req.body.resourceNames.length; i++) {
        if (req.body.resourceNames[i] && req.body.resourceUrls[i]) {
          moduleData.resources.push({
            name: req.body.resourceNames[i],
            description: req.body.resourceDescriptions[i] || '',
            url: req.body.resourceUrls[i],
            type: req.body.resourceTypes[i] || 'pdf'
          });
        }
      }
    } else if (req.body.resourceNames && req.body.resourceUrls) {
      // Handle single resource
      moduleData.resources.push({
        name: req.body.resourceNames,
        description: req.body.resourceDescriptions || '',
        url: req.body.resourceUrls,
        type: req.body.resourceTypes || 'pdf'
      });
    }

    const module = await createModule(req.params.courseId, moduleData);

    res.redirect(`/admin/courses/${req.params.courseId}/modules`);
  } catch (error) {
    console.error(`Error creating module for course ${req.params.courseId}:`, error);

    // Get the course again to display the form with the error
    const course = await getCourseById(req.params.courseId);

    res.render('admin/modules/new', {
      course,
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error creating module: ' + error.message,
      formData: req.body,
      layout: 'admin-layout'
    });
  }
});

// Edit module form
router.get('/courses/:courseId/modules/:moduleId/edit', async (req, res) => {
  try {
    const { courseId, moduleId } = req.params;

    const course = await getCourseById(courseId);
    const module = await getModuleById(courseId, moduleId);

    if (!course || !module) {
      return res.status(404).render('admin/error', {
        error: 'Course or module not found',
        message: 'The course or module you are trying to edit does not exist.',
        layout: 'admin-layout'
      });
    }

    res.render('admin/modules/edit', {
      course,
      module,
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error(`Error loading module ${req.params.moduleId} for editing:`, error);
    res.render('admin/error', {
      error: 'Error loading module',
      message: error.message,
      layout: 'admin-layout'
    });
  }
});

// Update module action
router.post('/courses/:courseId/modules/:moduleId', async (req, res) => {
  try {
    const { courseId, moduleId } = req.params;

    const moduleData = {
      title: req.body.title,
      description: req.body.description,
      content: req.body.content,
      videoUrl: req.body.videoUrl,
      duration: parseInt(req.body.duration) || 0,
      order: parseInt(req.body.order) || 0,
      isPublished: req.body.isPublished === 'on',
      resources: []
    };

    // Process resources if provided
    if (req.body.resourceNames && Array.isArray(req.body.resourceNames)) {
      for (let i = 0; i < req.body.resourceNames.length; i++) {
        if (req.body.resourceNames[i] && req.body.resourceUrls[i]) {
          moduleData.resources.push({
            name: req.body.resourceNames[i],
            description: req.body.resourceDescriptions[i] || '',
            url: req.body.resourceUrls[i],
            type: req.body.resourceTypes[i] || 'pdf'
          });
        }
      }
    } else if (req.body.resourceNames && req.body.resourceUrls) {
      // Handle single resource
      moduleData.resources.push({
        name: req.body.resourceNames,
        description: req.body.resourceDescriptions || '',
        url: req.body.resourceUrls,
        type: req.body.resourceTypes || 'pdf'
      });
    }

    await updateModule(courseId, moduleId, moduleData);

    res.redirect(`/admin/courses/${courseId}/modules`);
  } catch (error) {
    console.error(`Error updating module ${req.params.moduleId}:`, error);

    // Get the course and module again to display the form with the error
    const course = await getCourseById(req.params.courseId);
    const module = await getModuleById(req.params.courseId, req.params.moduleId);

    res.render('admin/modules/edit', {
      course,
      module,
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error updating module: ' + error.message,
      formData: req.body,
      layout: 'admin-layout'
    });
  }
});

// Delete module action
router.post('/courses/:courseId/modules/:moduleId/delete', async (req, res) => {
  try {
    const { courseId, moduleId } = req.params;

    await deleteModule(courseId, moduleId);

    res.redirect(`/admin/courses/${courseId}/modules`);
  } catch (error) {
    console.error(`Error deleting module ${req.params.moduleId}:`, error);
    res.redirect(`/admin/courses/${req.params.courseId}/modules?error=` + encodeURIComponent('Error deleting module: ' + error.message));
  }
});

// Reorder modules action
router.post('/courses/:courseId/modules/reorder', async (req, res) => {
  try {
    const { courseId } = req.params;
    const { moduleOrders } = req.body;

    if (!moduleOrders || !Array.isArray(moduleOrders)) {
      return res.status(400).json({ success: false, message: 'Invalid module order data' });
    }

    await reorderModules(courseId, moduleOrders);

    res.json({ success: true });
  } catch (error) {
    console.error(`Error reordering modules for course ${req.params.courseId}:`, error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Toggle module published status
router.post('/courses/:courseId/modules/:moduleId/toggle-published', async (req, res) => {
  try {
    const { courseId, moduleId } = req.params;

    const module = await getModuleById(courseId, moduleId);

    if (!module) {
      return res.status(404).json({ success: false, message: 'Module not found' });
    }

    await toggleModulePublished(courseId, moduleId, !module.isPublished);

    res.json({ success: true, isPublished: !module.isPublished });
  } catch (error) {
    console.error(`Error toggling published status for module ${req.params.moduleId}:`, error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// ==================== RESOURCES MANAGEMENT ====================

// Get all resources
router.get('/resources', async (req, res) => {
  try {
    const resources = await getAllResourcesAdmin();

    res.render('admin/resources/index', {
      resources,
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error('Error loading admin resources:', error);
    res.render('admin/resources/index', {
      resources: [],
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error loading resources: ' + error.message,
      layout: 'admin-layout'
    });
  }
});

// Create resource form
router.get('/resources/new', (req, res) => {
  res.render('admin/resources/new', {
    user: req.user,
    isAdmin: req.isAdmin,
    layout: 'admin-layout'
  });
});

// Create resource action
router.post('/resources', async (req, res) => {
  try {
    console.log('Resource creation request received:', req.body);

    // Check if user is available
    if (!req.user) {
      console.error('User not authenticated for resource creation');
      return res.status(401).render('admin/resources/new', {
        error: 'You must be logged in to create resources',
        formData: req.body,
        layout: 'admin-layout'
      });
    }

    console.log('User creating resource:', req.user);

    const resourceData = {
      title: req.body.title,
      summary: req.body.summary,
      content: req.body.content,
      category: req.body.category,
      imageDataUrl: req.body.imageDataUrl,
      isPublished: req.body.isPublished === 'on',
      tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : [],
      author: req.user.displayName || 'Admin',
      authorId: req.user.uid
    };

    console.log('Prepared resource data:', { ...resourceData, imageDataUrl: resourceData.imageDataUrl ? '[DATA URL]' : null });

    const resource = await createResource(resourceData);
    console.log('Resource created successfully:', resource.id);

    res.redirect('/admin/resources');
  } catch (error) {
    console.error('Error creating resource:', error);
    res.render('admin/resources/new', {
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error creating resource: ' + error.message,
      formData: req.body,
      layout: 'admin-layout'
    });
  }
});

// Debug route to test resource creation
router.get('/test-resource-creation', async (req, res) => {
  try {
    const testResource = {
      title: 'Test Resource ' + Date.now(),
      summary: 'This is a test resource created for debugging purposes.',
      content: 'Test content for the resource. This is automatically generated.',
      category: 'other',
      isPublished: true,
      tags: ['test', 'debug'],
      author: req.user ? req.user.displayName || 'Admin' : 'Debug User',
      authorId: req.user ? req.user.uid : 'debug-user-id'
    };

    console.log('Creating test resource:', testResource);
    const resource = await createResource(testResource);

    res.json({
      success: true,
      message: 'Test resource created successfully',
      resourceId: resource.id,
      resource: resource
    });
  } catch (error) {
    console.error('Error creating test resource:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Simple test form for resource creation
router.get('/resources/test-form', (req, res) => {
  res.render('admin/resources/test-form', {
    user: req.user,
    isAdmin: req.isAdmin,
    layout: 'admin-layout'
  });
});

// Direct resource creation route
router.get('/create-test-resource', async (req, res) => {
  try {
    // Create a simple test resource
    const testResource = {
      title: 'Auto-Generated Resource ' + new Date().toLocaleTimeString(),
      summary: 'This is an automatically generated test resource.',
      content: 'This content was created by the direct test route.',
      category: 'other',
      isPublished: true,
      tags: ['test', 'auto-generated'],
      author: req.user ? req.user.displayName || 'Admin' : 'System',
      authorId: req.user ? req.user.uid : 'system-user'
    };

    console.log('Creating resource via direct route:', testResource);
    await createResource(testResource);

    // Redirect to resources page
    res.redirect('/admin/resources');
  } catch (error) {
    console.error('Error in direct resource creation:', error);
    res.status(500).send('Error creating resource: ' + error.message);
  }
});

// Edit resource form
router.get('/resources/:id/edit', async (req, res) => {
  try {
    const resource = await getResourceById(req.params.id);

    if (!resource) {
      return res.status(404).render('admin/error', {
        error: 'Resource not found',
        message: 'The resource you are trying to edit does not exist.',
        layout: 'admin-layout'
      });
    }

    res.render('admin/resources/edit', {
      resource,
      user: req.user,
      isAdmin: req.isAdmin,
      layout: 'admin-layout'
    });
  } catch (error) {
    console.error(`Error loading resource ${req.params.id} for editing:`, error);
    res.render('admin/error', {
      error: 'Error loading resource',
      message: error.message,
      layout: 'admin-layout'
    });
  }
});

// Update resource action
router.post('/resources/:id', async (req, res) => {
  try {
    const resourceData = {
      title: req.body.title,
      summary: req.body.summary,
      content: req.body.content,
      category: req.body.category,
      imageDataUrl: req.body.imageDataUrl,
      isPublished: req.body.isPublished === 'on',
      tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : []
    };

    await updateResource(req.params.id, resourceData);

    res.redirect('/admin/resources');
  } catch (error) {
    console.error(`Error updating resource ${req.params.id}:`, error);

    // Get the resource again to display the form with the error
    const resource = await getResourceById(req.params.id);

    res.render('admin/resources/edit', {
      resource,
      user: req.user,
      isAdmin: req.isAdmin,
      error: 'Error updating resource: ' + error.message,
      formData: req.body,
      layout: 'admin-layout'
    });
  }
});

// Delete resource action
router.post('/resources/:id/delete', async (req, res) => {
  try {
    await deleteResource(req.params.id);

    res.redirect('/admin/resources');
  } catch (error) {
    console.error(`Error deleting resource ${req.params.id}:`, error);
    res.redirect('/admin/resources?error=' + encodeURIComponent('Error deleting resource: ' + error.message));
  }
});

// Toggle resource published status
router.post('/resources/:id/toggle-published', async (req, res) => {
  try {
    const resource = await getResourceById(req.params.id);

    if (!resource) {
      return res.status(404).json({ success: false, message: 'Resource not found' });
    }

    await toggleResourcePublished(req.params.id, !resource.isPublished);

    res.json({ success: true, isPublished: !resource.isPublished });
  } catch (error) {
    console.error(`Error toggling published status for resource ${req.params.id}:`, error);
    res.status(500).json({ success: false, message: error.message });
  }
});

export default router;

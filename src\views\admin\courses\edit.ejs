<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/admin">Admin Dashboard</a></li>
          <li class="breadcrumb-item"><a href="/admin/courses">Courses</a></li>
          <li class="breadcrumb-item active" aria-current="page">Edit Course</li>
        </ol>
      </nav>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-8">
      <h1>Edit Course</h1>
    </div>
    <div class="col-md-4 text-end">
      <a href="/admin/courses/<%= course.id %>/modules" class="btn btn-success">
        <i class="bi bi-collection"></i> Manage Modules
      </a>
    </div>
  </div>

  <% if (typeof error !== 'undefined') { %>
    <div class="row">
      <div class="col-md-12">
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row">
    <div class="col-md-8">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Details</h5>
        </div>
        <div class="card-body">
          <form action="/admin/courses/<%= course.id %>" method="POST" id="courseForm">
            <div class="mb-3">
              <label for="title" class="form-label">Course Title <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="title" name="title" required
                     value="<%= typeof formData !== 'undefined' ? formData.title : course.title %>">
            </div>
            
            <div class="mb-3">
              <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
              <textarea class="form-control" id="description" name="description" rows="4" required><%= typeof formData !== 'undefined' ? formData.description : course.description %></textarea>
              <div class="form-text">Provide a detailed description of what the course covers.</div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                <select class="form-select" id="category" name="category" required>
                  <option value="" disabled>Select a category</option>
                  <option value="organic-farming" <%= (typeof formData !== 'undefined' ? formData.category : course.category) === 'organic-farming' ? 'selected' : '' %>>Organic Farming</option>
                  <option value="water-conservation" <%= (typeof formData !== 'undefined' ? formData.category : course.category) === 'water-conservation' ? 'selected' : '' %>>Water Conservation</option>
                  <option value="renewable-energy" <%= (typeof formData !== 'undefined' ? formData.category : course.category) === 'renewable-energy' ? 'selected' : '' %>>Renewable Energy</option>
                  <option value="soil-health" <%= (typeof formData !== 'undefined' ? formData.category : course.category) === 'soil-health' ? 'selected' : '' %>>Soil Health</option>
                </select>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="level" class="form-label">Difficulty Level <span class="text-danger">*</span></label>
                <select class="form-select" id="level" name="level" required>
                  <option value="" disabled>Select a level</option>
                  <option value="beginner" <%= (typeof formData !== 'undefined' ? formData.level : course.level) === 'beginner' ? 'selected' : '' %>>Beginner</option>
                  <option value="intermediate" <%= (typeof formData !== 'undefined' ? formData.level : course.level) === 'intermediate' ? 'selected' : '' %>>Intermediate</option>
                  <option value="advanced" <%= (typeof formData !== 'undefined' ? formData.level : course.level) === 'advanced' ? 'selected' : '' %>>Advanced</option>
                </select>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="duration" class="form-label">Estimated Duration (minutes) <span class="text-danger">*</span></label>
              <input type="number" class="form-control" id="duration" name="duration" min="1" required
                     value="<%= typeof formData !== 'undefined' ? formData.duration : course.duration %>">
              <div class="form-text">Total estimated time to complete the course in minutes.</div>
            </div>
            
            <div class="mb-3">
              <label for="objectives" class="form-label">Learning Objectives</label>
              <textarea class="form-control" id="objectives" name="objectives" rows="3" placeholder="Enter each objective on a new line"><%= typeof formData !== 'undefined' ? formData.objectives : (course.objectives ? course.objectives.join('\n') : '') %></textarea>
              <div class="form-text">What will students learn from this course? Enter each objective on a new line.</div>
            </div>
            
            <div class="mb-3">
              <label for="prerequisites" class="form-label">Prerequisites</label>
              <textarea class="form-control" id="prerequisites" name="prerequisites" rows="3" placeholder="Enter each prerequisite on a new line"><%= typeof formData !== 'undefined' ? formData.prerequisites : (course.prerequisites ? course.prerequisites.join('\n') : '') %></textarea>
              <div class="form-text">What should students know before taking this course? Enter each prerequisite on a new line.</div>
            </div>
            
            <div class="mb-3">
              <label for="tags" class="form-label">Tags</label>
              <input type="text" class="form-control" id="tags" name="tags" placeholder="organic, certification, beginner"
                     value="<%= typeof formData !== 'undefined' ? formData.tags : (course.tags ? course.tags.join(', ') : '') %>">
              <div class="form-text">Comma-separated list of tags to help with search and categorization.</div>
            </div>

            <!-- Premium Course Settings -->
            <div class="card border-warning mb-3">
              <div class="card-header bg-warning bg-opacity-10">
                <h6 class="mb-0"><i class="bi bi-star-fill text-warning"></i> Premium Course Settings</h6>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" role="switch" id="isPremium" name="isPremium"
                           <%= (typeof formData !== 'undefined' ? formData.isPremium === 'on' : course.isPremium) ? 'checked' : '' %>>
                    <label class="form-check-label" for="isPremium">
                      <strong>Premium Course</strong>
                    </label>
                  </div>
                  <div class="form-text">Premium courses require payment and admin approval for enrollment.</div>
                </div>

                <div id="premiumSettings" style="display: <%= (typeof formData !== 'undefined' ? formData.isPremium === 'on' : course.isPremium) ? 'block' : 'none' %>;">
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="price" class="form-label">Course Price (USD) <span class="text-danger">*</span></label>
                      <div class="input-group">
                        <span class="input-group-text">$</span>
                        <input type="number" class="form-control" id="price" name="price" min="0" step="0.01"
                               value="<%= typeof formData !== 'undefined' ? formData.price : (course.price || '') %>">
                      </div>
                      <div class="form-text">Set to 0 for free premium courses.</div>
                    </div>

                    <div class="col-md-6 mb-3">
                      <label for="currency" class="form-label">Currency</label>
                      <select class="form-select" id="currency" name="currency">
                        <option value="USD" <%= (typeof formData !== 'undefined' ? formData.currency === 'USD' : course.currency === 'USD') ? 'selected' : '' %>>USD ($)</option>
                        <option value="EUR" <%= (typeof formData !== 'undefined' ? formData.currency === 'EUR' : course.currency === 'EUR') ? 'selected' : '' %>>EUR (€)</option>
                        <option value="GBP" <%= (typeof formData !== 'undefined' ? formData.currency === 'GBP' : course.currency === 'GBP') ? 'selected' : '' %>>GBP (£)</option>
                        <option value="XAF" <%= (typeof formData !== 'undefined' ? formData.currency === 'XAF' : course.currency === 'XAF') ? 'selected' : '' %>>XAF (Central African CFA Franc)</option>
                      </select>
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="enrollmentLimit" class="form-label">Enrollment Limit</label>
                    <input type="number" class="form-control" id="enrollmentLimit" name="enrollmentLimit" min="1"
                           value="<%= typeof formData !== 'undefined' ? formData.enrollmentLimit : (course.enrollmentLimit || '') %>">
                    <div class="form-text">Maximum number of students (leave empty for unlimited).</div>
                  </div>

                  <div class="mb-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="requiresApproval" name="requiresApproval"
                             <%= (typeof formData !== 'undefined' ? formData.requiresApproval === 'on' : course.requiresApproval) ? 'checked' : '' %>>
                      <label class="form-check-label" for="requiresApproval">
                        Requires Admin Approval
                      </label>
                    </div>
                    <div class="form-text">Students must be approved by admin before accessing the course.</div>
                  </div>

                  <div class="mb-3">
                    <label for="certificateTemplate" class="form-label">Certificate Template</label>
                    <select class="form-select" id="certificateTemplate" name="certificateTemplate">
                      <option value="" <%= (typeof formData !== 'undefined' ? !formData.certificateTemplate : !course.certificateTemplate) ? 'selected' : '' %>>No Certificate</option>
                      <option value="basic" <%= (typeof formData !== 'undefined' ? formData.certificateTemplate === 'basic' : course.certificateTemplate === 'basic') ? 'selected' : '' %>>Basic Certificate</option>
                      <option value="advanced" <%= (typeof formData !== 'undefined' ? formData.certificateTemplate === 'advanced' : course.certificateTemplate === 'advanced') ? 'selected' : '' %>>Advanced Certificate</option>
                      <option value="professional" <%= (typeof formData !== 'undefined' ? formData.certificateTemplate === 'professional' : course.certificateTemplate === 'professional') ? 'selected' : '' %>>Professional Certificate</option>
                    </select>
                    <div class="form-text">Certificate awarded upon course completion.</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" role="switch" id="isPublished" name="isPublished"
                       <%= (typeof formData !== 'undefined' ? formData.isPublished === 'on' : course.isPublished) ? 'checked' : '' %>>
                <label class="form-check-label" for="isPublished">Published</label>
              </div>
              <div class="form-text">Toggle to publish or unpublish this course.</div>
            </div>
            
            <input type="hidden" id="imageDataUrl" name="imageDataUrl" value="">
            
            <div class="d-flex justify-content-between">
              <a href="/admin/courses" class="btn btn-outline-secondary">Cancel</a>
              <button type="submit" class="btn btn-success">Save Changes</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Image</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <div id="imagePreview" class="mb-3">
              <% if (course.imageUrl) { %>
                <img src="<%= course.imageUrl %>" alt="<%= course.title %>" class="img-fluid rounded" style="max-height: 200px;">
              <% } else { %>
                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                  <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                </div>
              <% } %>
            </div>
            <div class="d-grid">
              <button type="button" class="btn btn-outline-success" id="uploadImageBtn">
                <i class="bi bi-upload"></i> Change Image
              </button>
            </div>
            <input type="file" id="imageInput" accept="image/*" style="display: none;">
          </div>
          <div class="small text-muted">
            <p>Recommended image size: 1200 x 800 pixels</p>
            <p>Maximum file size: 5MB</p>
            <p>Supported formats: JPG, PNG</p>
          </div>
        </div>
      </div>
      
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Statistics</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Modules
              <span class="badge bg-success rounded-pill"><%= course.moduleCount || 0 %></span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Enrollments
              <span class="badge bg-success rounded-pill"><%= course.enrollmentCount || 0 %></span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Created
              <span><%= new Date(course.createdAt).toLocaleDateString() %></span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Last Updated
              <span><%= new Date(course.updatedAt).toLocaleDateString() %></span>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <h5 class="mb-0">Actions</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="/courses/<%= course.id %>" class="btn btn-outline-primary" target="_blank">
              <i class="bi bi-eye"></i> Preview Course
            </a>
            <a href="/admin/courses/<%= course.id %>/modules" class="btn btn-outline-success">
              <i class="bi bi-collection"></i> Manage Modules
            </a>
            <button type="button" class="btn btn-outline-danger" onclick="confirmDeleteCourse('<%= course.id %>', '<%= course.title %>')">
              <i class="bi bi-trash"></i> Delete Course
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Course Modal -->
<div class="modal fade" id="deleteCourseModal" tabindex="-1" aria-labelledby="deleteCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteCourseModalLabel">Confirm Deletion</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the course "<span id="courseTitle"></span>"?</p>
        <p class="text-danger">This action cannot be undone. All modules and content will be permanently deleted.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <form id="deleteCourseForm" method="POST" action="">
          <button type="submit" class="btn btn-danger">Delete Course</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const uploadImageBtn = document.getElementById('uploadImageBtn');
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const imageDataUrl = document.getElementById('imageDataUrl');
    const isPremiumCheckbox = document.getElementById('isPremium');
    const premiumSettings = document.getElementById('premiumSettings');
    const priceInput = document.getElementById('price');

    // Handle premium course toggle
    function togglePremiumSettings() {
      if (isPremiumCheckbox.checked) {
        premiumSettings.style.display = 'block';
        priceInput.required = true;
      } else {
        premiumSettings.style.display = 'none';
        priceInput.required = false;
      }
    }

    isPremiumCheckbox.addEventListener('change', togglePremiumSettings);

    uploadImageBtn.addEventListener('click', function() {
      imageInput.click();
    });
    
    imageInput.addEventListener('change', function(e) {
      if (e.target.files.length === 0) {
        return;
      }
      
      const file = e.target.files[0];
      
      // Check file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert('File is too large. Maximum size is 5MB.');
        return;
      }
      
      // Check file type
      if (!file.type.match('image/jpeg') && !file.type.match('image/png')) {
        alert('Only JPG and PNG files are supported.');
        return;
      }
      
      const reader = new FileReader();
      
      reader.onload = function(event) {
        const img = document.createElement('img');
        img.src = event.target.result;
        img.className = 'img-fluid rounded';
        img.style.maxHeight = '200px';
        
        // Clear the preview and add the new image
        imagePreview.innerHTML = '';
        imagePreview.appendChild(img);
        
        // Store the data URL for form submission
        imageDataUrl.value = event.target.result;
      };
      
      reader.readAsDataURL(file);
    });
  });
  
  function confirmDeleteCourse(courseId, courseTitle) {
    document.getElementById('courseTitle').textContent = courseTitle;
    document.getElementById('deleteCourseForm').action = `/admin/courses/${courseId}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteCourseModal'));
    modal.show();
  }
</script>

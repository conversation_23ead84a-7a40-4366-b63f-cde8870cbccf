/**
 * Admin Dashboard JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize CKEditor on content textareas if they exist
  if (typeof ClassicEditor !== 'undefined') {
    const contentElements = document.querySelectorAll('.content-editor');
    
    contentElements.forEach(element => {
      ClassicEditor
        .create(element, {
          toolbar: [
            'heading',
            '|',
            'bold',
            'italic',
            'link',
            'bulletedList',
            'numberedList',
            '|',
            'outdent',
            'indent',
            '|',
            'blockQuote',
            'insertTable',
            'undo',
            'redo'
          ]
        })
        .catch(error => {
          console.error('Error initializing editor:', error);
        });
    });
  }
  
  // Highlight current page in sidebar
  const currentPath = window.location.pathname;
  const sidebarLinks = document.querySelectorAll('#adminSidebar .nav-link');
  
  sidebarLinks.forEach(link => {
    const href = link.getAttribute('href');
    
    if (currentPath === href || 
        (href !== '/admin' && currentPath.startsWith(href))) {
      link.classList.add('active');
    }
  });
  
  // Toggle sidebar on mobile
  const sidebarToggle = document.getElementById('sidebarToggle');
  if (sidebarToggle) {
    sidebarToggle.addEventListener('click', function() {
      document.querySelector('.sidebar').classList.toggle('show');
    });
  }
  
  // Initialize tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function(tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
  
  // Initialize popovers
  const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
  popoverTriggerList.map(function(popoverTriggerEl) {
    return new bootstrap.Popover(popoverTriggerEl);
  });
  
  // Confirm delete actions
  const confirmDeleteForms = document.querySelectorAll('.confirm-delete-form');
  confirmDeleteForms.forEach(form => {
    form.addEventListener('submit', function(e) {
      const confirmed = confirm('Are you sure you want to delete this item? This action cannot be undone.');
      if (!confirmed) {
        e.preventDefault();
      }
    });
  });
  
  // Image upload preview
  const imageInput = document.getElementById('imageInput');
  const imagePreview = document.getElementById('imagePreview');
  const imageDataUrl = document.getElementById('imageDataUrl');
  
  if (imageInput && imagePreview) {
    imageInput.addEventListener('change', function(e) {
      if (e.target.files.length === 0) {
        return;
      }
      
      const file = e.target.files[0];
      
      // Check file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert('File is too large. Maximum size is 5MB.');
        return;
      }
      
      // Check file type
      if (!file.type.match('image/jpeg') && !file.type.match('image/png')) {
        alert('Only JPG and PNG files are supported.');
        return;
      }
      
      const reader = new FileReader();
      
      reader.onload = function(event) {
        const img = document.createElement('img');
        img.src = event.target.result;
        img.className = 'img-fluid rounded';
        
        // Clear the preview and add the new image
        imagePreview.innerHTML = '';
        imagePreview.appendChild(img);
        
        // Store the data URL for form submission
        if (imageDataUrl) {
          imageDataUrl.value = event.target.result;
        }
      };
      
      reader.readAsDataURL(file);
    });
  }
  
  // Resource management
  const addResourceBtn = document.getElementById('addResourceBtn');
  const resourcesContainer = document.getElementById('resourcesContainer');
  const resourceTemplate = document.getElementById('resourceTemplate');
  
  if (addResourceBtn && resourcesContainer && resourceTemplate) {
    // Add resource
    addResourceBtn.addEventListener('click', function() {
      const resourceItem = document.importNode(resourceTemplate.content, true);
      resourcesContainer.appendChild(resourceItem);
      
      // Add event listener to the new remove button
      const removeBtn = resourcesContainer.lastElementChild.querySelector('.remove-resource');
      removeBtn.addEventListener('click', function() {
        this.closest('.resource-item').remove();
      });
    });
    
    // Add event listeners to existing remove buttons
    document.querySelectorAll('.remove-resource').forEach(button => {
      button.addEventListener('click', function() {
        this.closest('.resource-item').remove();
      });
    });
  }
});

import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';
import {
  ref,
  uploadString,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { db, storage } from '../config/initFirebase.js';
import { getCurrentUser } from './firebaseService.js';

// Collection names
const COURSES_COLLECTION = 'courses';
const MODULES_COLLECTION = 'modules';

/**
 * Create a new course
 */
export const createCourse = async (courseData) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to create a course');
    }
    
    // Prepare course data
    const newCourse = {
      ...courseData,
      authorId: user.uid,
      authorName: user.displayName || 'Admin User',
      moduleCount: 0,
      enrollmentCount: 0,
      isPublished: courseData.isPublished || false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    
    // Upload image if provided
    if (courseData.imageDataUrl) {
      // Generate a unique filename
      const timestamp = Date.now();
      const filename = `course_${timestamp}.jpg`;
      
      // Create a reference to the file in Firebase Storage
      const fileRef = ref(storage, `course_images/${filename}`);
      
      // Upload the file to Firebase Storage
      await uploadString(fileRef, courseData.imageDataUrl, 'data_url');
      
      // Get the download URL
      const imageUrl = await getDownloadURL(fileRef);
      
      // Add the image URL to the course data
      newCourse.imageUrl = imageUrl;
      
      // Remove the data URL from the course data
      delete newCourse.imageDataUrl;
    }
    
    // Add the course to Firestore
    const courseRef = await addDoc(collection(db, COURSES_COLLECTION), newCourse);
    
    return {
      id: courseRef.id,
      ...newCourse
    };
  } catch (error) {
    console.error('Error creating course:', error);
    throw error;
  }
};

/**
 * Update an existing course
 */
export const updateCourse = async (courseId, courseData) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to update a course');
    }
    
    // Get the existing course
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (!courseDoc.exists()) {
      throw new Error('Course not found');
    }
    
    const existingCourse = courseDoc.data();
    
    // Prepare update data
    const updateData = {
      ...courseData,
      updatedAt: serverTimestamp()
    };
    
    // Upload new image if provided
    if (courseData.imageDataUrl) {
      // Generate a unique filename
      const timestamp = Date.now();
      const filename = `course_${timestamp}.jpg`;
      
      // Create a reference to the file in Firebase Storage
      const fileRef = ref(storage, `course_images/${filename}`);
      
      // Upload the file to Firebase Storage
      await uploadString(fileRef, courseData.imageDataUrl, 'data_url');
      
      // Get the download URL
      const imageUrl = await getDownloadURL(fileRef);
      
      // Add the image URL to the update data
      updateData.imageUrl = imageUrl;
      
      // Remove the data URL from the update data
      delete updateData.imageDataUrl;
      
      // Delete the old image if it exists
      if (existingCourse.imageUrl && existingCourse.imageUrl.includes('firebasestorage.googleapis.com')) {
        try {
          const oldFileRef = ref(storage, existingCourse.imageUrl);
          await deleteObject(oldFileRef);
        } catch (deleteError) {
          console.error('Error deleting old image:', deleteError);
          // Continue with the update even if image deletion fails
        }
      }
    }
    
    // Update the course in Firestore
    await updateDoc(courseRef, updateData);
    
    return {
      id: courseId,
      ...existingCourse,
      ...updateData
    };
  } catch (error) {
    console.error(`Error updating course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Delete a course
 */
export const deleteCourse = async (courseId) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to delete a course');
    }
    
    // Get the course to check if it exists and get the image URL
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (!courseDoc.exists()) {
      throw new Error('Course not found');
    }
    
    const course = courseDoc.data();
    
    // Delete the course image if it exists
    if (course.imageUrl && course.imageUrl.includes('firebasestorage.googleapis.com')) {
      try {
        const fileRef = ref(storage, course.imageUrl);
        await deleteObject(fileRef);
      } catch (deleteError) {
        console.error('Error deleting course image:', deleteError);
        // Continue with the deletion even if image deletion fails
      }
    }
    
    // Get all modules for the course
    const modulesQuery = query(
      collection(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION)
    );
    
    const modulesSnapshot = await getDocs(modulesQuery);
    
    // Delete all modules
    const moduleDeletePromises = [];
    modulesSnapshot.forEach((moduleDoc) => {
      const moduleRef = doc(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION, moduleDoc.id);
      moduleDeletePromises.push(deleteDoc(moduleRef));
    });
    
    await Promise.all(moduleDeletePromises);
    
    // Delete the course
    await deleteDoc(courseRef);
    
    return true;
  } catch (error) {
    console.error(`Error deleting course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Get all courses (including unpublished) for admin
 */
export const getAllCoursesAdmin = async () => {
  try {
    const coursesQuery = query(
      collection(db, COURSES_COLLECTION),
      orderBy('createdAt', 'desc')
    );
    
    const coursesSnapshot = await getDocs(coursesQuery);
    
    const courses = [];
    coursesSnapshot.forEach((doc) => {
      courses.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return courses;
  } catch (error) {
    console.error('Error getting all courses for admin:', error);
    throw error;
  }
};

/**
 * Create a new module for a course
 */
export const createModule = async (courseId, moduleData) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to create a module');
    }
    
    // Check if the course exists
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (!courseDoc.exists()) {
      throw new Error('Course not found');
    }
    
    // Get the current module count
    const course = courseDoc.data();
    const currentModuleCount = course.moduleCount || 0;
    
    // Prepare module data
    const newModule = {
      ...moduleData,
      courseId,
      order: moduleData.order || currentModuleCount + 1,
      isPublished: moduleData.isPublished || false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    
    // Add the module to Firestore
    const moduleRef = await addDoc(
      collection(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION),
      newModule
    );
    
    // Update the module count in the course
    await updateDoc(courseRef, {
      moduleCount: currentModuleCount + 1,
      updatedAt: serverTimestamp()
    });
    
    return {
      id: moduleRef.id,
      ...newModule
    };
  } catch (error) {
    console.error(`Error creating module for course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Update an existing module
 */
export const updateModule = async (courseId, moduleId, moduleData) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to update a module');
    }
    
    // Check if the module exists
    const moduleRef = doc(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION, moduleId);
    const moduleDoc = await getDoc(moduleRef);
    
    if (!moduleDoc.exists()) {
      throw new Error('Module not found');
    }
    
    // Prepare update data
    const updateData = {
      ...moduleData,
      updatedAt: serverTimestamp()
    };
    
    // Update the module in Firestore
    await updateDoc(moduleRef, updateData);
    
    return {
      id: moduleId,
      courseId,
      ...moduleDoc.data(),
      ...updateData
    };
  } catch (error) {
    console.error(`Error updating module ${moduleId}:`, error);
    throw error;
  }
};

/**
 * Delete a module
 */
export const deleteModule = async (courseId, moduleId) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to delete a module');
    }
    
    // Check if the module exists
    const moduleRef = doc(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION, moduleId);
    const moduleDoc = await getDoc(moduleRef);
    
    if (!moduleDoc.exists()) {
      throw new Error('Module not found');
    }
    
    // Get the course to update the module count
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (!courseDoc.exists()) {
      throw new Error('Course not found');
    }
    
    const course = courseDoc.data();
    const currentModuleCount = course.moduleCount || 0;
    
    // Delete the module
    await deleteDoc(moduleRef);
    
    // Update the module count in the course
    await updateDoc(courseRef, {
      moduleCount: Math.max(0, currentModuleCount - 1),
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error(`Error deleting module ${moduleId}:`, error);
    throw error;
  }
};

/**
 * Reorder modules
 */
export const reorderModules = async (courseId, moduleOrders) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to reorder modules');
    }
    
    // Check if the course exists
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (!courseDoc.exists()) {
      throw new Error('Course not found');
    }
    
    // Update the order of each module
    const updatePromises = [];
    
    for (const item of moduleOrders) {
      const moduleRef = doc(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION, item.moduleId);
      updatePromises.push(
        updateDoc(moduleRef, {
          order: item.order,
          updatedAt: serverTimestamp()
        })
      );
    }
    
    await Promise.all(updatePromises);
    
    // Update the course's updatedAt timestamp
    await updateDoc(courseRef, {
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error(`Error reordering modules for course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Get all modules for a course (including unpublished) for admin
 */
export const getAllModulesAdmin = async (courseId) => {
  try {
    const modulesQuery = query(
      collection(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION),
      orderBy('order', 'asc')
    );
    
    const modulesSnapshot = await getDocs(modulesQuery);
    
    const modules = [];
    modulesSnapshot.forEach((doc) => {
      modules.push({
        id: doc.id,
        courseId,
        ...doc.data()
      });
    });
    
    return modules;
  } catch (error) {
    console.error(`Error getting all modules for course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Publish or unpublish a course
 */
export const toggleCoursePublished = async (courseId, isPublished) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to publish/unpublish a course');
    }
    
    // Check if the course exists
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (!courseDoc.exists()) {
      throw new Error('Course not found');
    }
    
    // Update the published status
    await updateDoc(courseRef, {
      isPublished,
      updatedAt: serverTimestamp()
    });
    
    return {
      id: courseId,
      ...courseDoc.data(),
      isPublished
    };
  } catch (error) {
    console.error(`Error toggling published status for course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Publish or unpublish a module
 */
export const toggleModulePublished = async (courseId, moduleId, isPublished) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to publish/unpublish a module');
    }
    
    // Check if the module exists
    const moduleRef = doc(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION, moduleId);
    const moduleDoc = await getDoc(moduleRef);
    
    if (!moduleDoc.exists()) {
      throw new Error('Module not found');
    }
    
    // Update the published status
    await updateDoc(moduleRef, {
      isPublished,
      updatedAt: serverTimestamp()
    });
    
    return {
      id: moduleId,
      courseId,
      ...moduleDoc.data(),
      isPublished
    };
  } catch (error) {
    console.error(`Error toggling published status for module ${moduleId}:`, error);
    throw error;
  }
};

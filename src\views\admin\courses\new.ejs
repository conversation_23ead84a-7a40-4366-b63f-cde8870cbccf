<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/admin">Admin Dashboard</a></li>
          <li class="breadcrumb-item"><a href="/admin/courses">Courses</a></li>
          <li class="breadcrumb-item active" aria-current="page">Create New Course</li>
        </ol>
      </nav>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-12">
      <h1>Create New Course</h1>
    </div>
  </div>

  <% if (typeof error !== 'undefined') { %>
    <div class="row">
      <div class="col-md-12">
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row">
    <div class="col-md-8">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Details</h5>
        </div>
        <div class="card-body">
          <form action="/admin/courses" method="POST" id="courseForm">
            <div class="mb-3">
              <label for="title" class="form-label">Course Title <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="title" name="title" required
                     value="<%= typeof formData !== 'undefined' ? formData.title : '' %>">
            </div>
            
            <div class="mb-3">
              <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
              <textarea class="form-control" id="description" name="description" rows="4" required><%= typeof formData !== 'undefined' ? formData.description : '' %></textarea>
              <div class="form-text">Provide a detailed description of what the course covers.</div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                <select class="form-select" id="category" name="category" required>
                  <option value="" disabled <%= typeof formData === 'undefined' || !formData.category ? 'selected' : '' %>>Select a category</option>
                  <option value="organic-farming" <%= typeof formData !== 'undefined' && formData.category === 'organic-farming' ? 'selected' : '' %>>Organic Farming</option>
                  <option value="water-conservation" <%= typeof formData !== 'undefined' && formData.category === 'water-conservation' ? 'selected' : '' %>>Water Conservation</option>
                  <option value="renewable-energy" <%= typeof formData !== 'undefined' && formData.category === 'renewable-energy' ? 'selected' : '' %>>Renewable Energy</option>
                  <option value="soil-health" <%= typeof formData !== 'undefined' && formData.category === 'soil-health' ? 'selected' : '' %>>Soil Health</option>
                </select>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="level" class="form-label">Difficulty Level <span class="text-danger">*</span></label>
                <select class="form-select" id="level" name="level" required>
                  <option value="" disabled <%= typeof formData === 'undefined' || !formData.level ? 'selected' : '' %>>Select a level</option>
                  <option value="beginner" <%= typeof formData !== 'undefined' && formData.level === 'beginner' ? 'selected' : '' %>>Beginner</option>
                  <option value="intermediate" <%= typeof formData !== 'undefined' && formData.level === 'intermediate' ? 'selected' : '' %>>Intermediate</option>
                  <option value="advanced" <%= typeof formData !== 'undefined' && formData.level === 'advanced' ? 'selected' : '' %>>Advanced</option>
                </select>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="duration" class="form-label">Estimated Duration (minutes) <span class="text-danger">*</span></label>
              <input type="number" class="form-control" id="duration" name="duration" min="1" required
                     value="<%= typeof formData !== 'undefined' ? formData.duration : '' %>">
              <div class="form-text">Total estimated time to complete the course in minutes.</div>
            </div>
            
            <div class="mb-3">
              <label for="objectives" class="form-label">Learning Objectives</label>
              <textarea class="form-control" id="objectives" name="objectives" rows="3" placeholder="Enter each objective on a new line"><%= typeof formData !== 'undefined' ? formData.objectives : '' %></textarea>
              <div class="form-text">What will students learn from this course? Enter each objective on a new line.</div>
            </div>
            
            <div class="mb-3">
              <label for="prerequisites" class="form-label">Prerequisites</label>
              <textarea class="form-control" id="prerequisites" name="prerequisites" rows="3" placeholder="Enter each prerequisite on a new line"><%= typeof formData !== 'undefined' ? formData.prerequisites : '' %></textarea>
              <div class="form-text">What should students know before taking this course? Enter each prerequisite on a new line.</div>
            </div>
            
            <div class="mb-3">
              <label for="tags" class="form-label">Tags</label>
              <input type="text" class="form-control" id="tags" name="tags" placeholder="organic, certification, beginner"
                     value="<%= typeof formData !== 'undefined' ? formData.tags : '' %>">
              <div class="form-text">Comma-separated list of tags to help with search and categorization.</div>
            </div>
            
            <div class="mb-3">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" role="switch" id="isPublished" name="isPublished"
                       <%= typeof formData !== 'undefined' && formData.isPublished === 'on' ? 'checked' : '' %>>
                <label class="form-check-label" for="isPublished">Publish course immediately</label>
              </div>
              <div class="form-text">If unchecked, the course will be saved as a draft.</div>
            </div>
            
            <input type="hidden" id="imageDataUrl" name="imageDataUrl" value="">
            
            <div class="d-flex justify-content-between">
              <a href="/admin/courses" class="btn btn-outline-secondary">Cancel</a>
              <button type="submit" class="btn btn-success">Create Course</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Image</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <div id="imagePreview" class="mb-3">
              <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
              </div>
            </div>
            <div class="d-grid">
              <button type="button" class="btn btn-outline-success" id="uploadImageBtn">
                <i class="bi bi-upload"></i> Upload Image
              </button>
            </div>
            <input type="file" id="imageInput" accept="image/*" style="display: none;">
          </div>
          <div class="small text-muted">
            <p>Recommended image size: 1200 x 800 pixels</p>
            <p>Maximum file size: 5MB</p>
            <p>Supported formats: JPG, PNG</p>
          </div>
        </div>
      </div>
      
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <h5 class="mb-0">Next Steps</h5>
        </div>
        <div class="card-body">
          <p>After creating the course, you'll be able to:</p>
          <ul>
            <li>Add modules to the course</li>
            <li>Upload resources for each module</li>
            <li>Preview the course as a student</li>
            <li>Publish the course when it's ready</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const uploadImageBtn = document.getElementById('uploadImageBtn');
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const imageDataUrl = document.getElementById('imageDataUrl');
    
    uploadImageBtn.addEventListener('click', function() {
      imageInput.click();
    });
    
    imageInput.addEventListener('change', function(e) {
      if (e.target.files.length === 0) {
        return;
      }
      
      const file = e.target.files[0];
      
      // Check file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert('File is too large. Maximum size is 5MB.');
        return;
      }
      
      // Check file type
      if (!file.type.match('image/jpeg') && !file.type.match('image/png')) {
        alert('Only JPG and PNG files are supported.');
        return;
      }
      
      const reader = new FileReader();
      
      reader.onload = function(event) {
        const img = document.createElement('img');
        img.src = event.target.result;
        img.className = 'img-fluid rounded';
        img.style.maxHeight = '200px';
        
        // Clear the preview and add the new image
        imagePreview.innerHTML = '';
        imagePreview.appendChild(img);
        
        // Store the data URL for form submission
        imageDataUrl.value = event.target.result;
      };
      
      reader.readAsDataURL(file);
    });
  });
</script>

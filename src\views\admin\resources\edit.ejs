<div class="container">
  <div class="row">
    <div class="col-md-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Resource</h1>
        <a href="/admin/resources" class="btn btn-outline-secondary">
          <i class="bi bi-arrow-left"></i> Back to Resources
        </a>
      </div>
      
      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      <% } %>
      
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h4 class="mb-0">Resource Details</h4>
        </div>
        <div class="card-body">
          <form action="/admin/resources/<%= resource.id %>" method="POST" id="resourceForm">
            <div class="row mb-3">
              <div class="col-md-8">
                <div class="mb-3">
                  <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="title" name="title" required 
                         value="<%= typeof formData !== 'undefined' ? formData.title : resource.title %>">
                </div>
                
                <div class="mb-3">
                  <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                  <select class="form-select" id="category" name="category" required>
                    <option value="" disabled>Select a category</option>
                    <option value="organic-farming" <%= (typeof formData !== 'undefined' ? formData.category : resource.category) === 'organic-farming' ? 'selected' : '' %>>Organic Farming</option>
                    <option value="water-conservation" <%= (typeof formData !== 'undefined' ? formData.category : resource.category) === 'water-conservation' ? 'selected' : '' %>>Water Conservation</option>
                    <option value="renewable-energy" <%= (typeof formData !== 'undefined' ? formData.category : resource.category) === 'renewable-energy' ? 'selected' : '' %>>Renewable Energy</option>
                    <option value="soil-health" <%= (typeof formData !== 'undefined' ? formData.category : resource.category) === 'soil-health' ? 'selected' : '' %>>Soil Health</option>
                    <option value="pest-management" <%= (typeof formData !== 'undefined' ? formData.category : resource.category) === 'pest-management' ? 'selected' : '' %>>Pest Management</option>
                    <option value="livestock" <%= (typeof formData !== 'undefined' ? formData.category : resource.category) === 'livestock' ? 'selected' : '' %>>Livestock</option>
                    <option value="other" <%= (typeof formData !== 'undefined' ? formData.category : resource.category) === 'other' ? 'selected' : '' %>>Other</option>
                  </select>
                </div>
                
                <div class="mb-3">
                  <label for="summary" class="form-label">Summary <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="summary" name="summary" rows="3" required><%= typeof formData !== 'undefined' ? formData.summary : resource.summary %></textarea>
                  <div class="form-text">A brief summary that will appear in resource listings (100-150 characters recommended).</div>
                </div>
                
                <div class="mb-3">
                  <label for="tags" class="form-label">Tags</label>
                  <input type="text" class="form-control" id="tags" name="tags" 
                         value="<%= typeof formData !== 'undefined' ? formData.tags : (resource.tags ? resource.tags.join(', ') : '') %>">
                  <div class="form-text">Comma-separated list of tags (e.g., organic, water, soil).</div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="mb-3">
                  <label class="form-label">Featured Image</label>
                  <div class="card">
                    <div class="card-body text-center">
                      <div id="imagePreview" class="mb-3 d-flex justify-content-center align-items-center bg-light" style="height: 200px; overflow: hidden;">
                        <% if (resource.imageUrl) { %>
                          <img src="<%= resource.imageUrl %>" class="img-fluid" alt="<%= resource.title %>">
                        <% } else { %>
                          <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                        <% } %>
                      </div>
                      <input type="file" class="form-control mb-2" id="imageUpload" accept="image/*">
                      <input type="hidden" id="imageDataUrl" name="imageDataUrl">
                      <div class="form-text">Recommended size: 800x600 pixels.</div>
                    </div>
                  </div>
                </div>
                
                <div class="mb-3">
                  <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="isPublished" name="isPublished" 
                           <%= (typeof formData !== 'undefined' ? formData.isPublished : resource.isPublished) ? 'checked' : '' %>>
                    <label class="form-check-label" for="isPublished">Published</label>
                  </div>
                  <div class="form-text">Toggle to publish or unpublish this resource.</div>
                </div>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
              <textarea class="form-control" id="content" name="content" rows="15" required><%= typeof formData !== 'undefined' ? formData.content : resource.content %></textarea>
              <div class="form-text">You can use Markdown formatting for the content.</div>
            </div>
            
            <div class="d-flex justify-content-between">
              <a href="/admin/resources" class="btn btn-outline-secondary">Cancel</a>
              <div>
                <button type="button" class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#deleteResourceModal">
                  Delete Resource
                </button>
                <button type="submit" class="btn btn-success">Update Resource</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteResourceModal" tabindex="-1" aria-labelledby="deleteResourceModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="deleteResourceModalLabel">Confirm Delete</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the resource "<%= resource.title %>"?</p>
        <p class="text-danger"><strong>This action cannot be undone.</strong></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <form action="/admin/resources/<%= resource.id %>/delete" method="POST">
          <button type="submit" class="btn btn-danger">Delete Resource</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Image upload preview
    const imageUpload = document.getElementById('imageUpload');
    const imagePreview = document.getElementById('imagePreview');
    const imageDataUrl = document.getElementById('imageDataUrl');

    // Debug: Check if elements exist
    console.log('Resource edit form - Image upload:', imageUpload);
    console.log('Resource edit form - Image preview:', imagePreview);
    console.log('Resource edit form - Image data URL:', imageDataUrl);

    if (!imageUpload || !imagePreview || !imageDataUrl) {
      console.error('Missing required elements for resource image upload in edit form');
      alert('Image upload functionality is not properly initialized. Please refresh the page.');
      return;
    }

    imageUpload.addEventListener('change', function(e) {
      console.log('Resource edit form - File input changed', e.target.files);

      if (this.files && this.files[0]) {
        const file = this.files[0];
        console.log('Resource edit form - Selected file:', file.name, file.size, file.type);

        // Check file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          alert('File is too large. Maximum size is 5MB.');
          console.error('Resource edit form - File too large:', file.size);
          this.value = ''; // Clear the input
          return;
        }

        // Check file type
        if (!file.type.match('image/jpeg') && !file.type.match('image/png') &&
            !file.type.match('image/webp') && !file.type.match('image/gif')) {
          alert('Only JPG, PNG, WebP, and GIF files are supported.');
          console.error('Resource edit form - Invalid file type:', file.type);
          this.value = ''; // Clear the input
          return;
        }

        console.log('Resource edit form - File validation passed, reading file...');
        const reader = new FileReader();

        reader.onload = function(e) {
          console.log('Resource edit form - File read successfully, data URL length:', e.target.result.length);

          // Clear the preview
          imagePreview.innerHTML = '';

          // Create image element
          const img = document.createElement('img');
          img.src = e.target.result;
          img.classList.add('img-fluid', 'rounded');
          img.style.maxHeight = '200px';
          img.style.width = 'auto';

          // Store the data URL
          imageDataUrl.value = e.target.result;

          // Add image to preview
          imagePreview.appendChild(img);

          console.log('Resource edit form - Image preview updated and data URL stored');

          // Show success message
          const successMsg = document.createElement('div');
          successMsg.className = 'alert alert-success mt-2';
          successMsg.innerHTML = '<i class="bi bi-check-circle"></i> Image updated successfully!';
          imagePreview.appendChild(successMsg);

          setTimeout(() => {
            if (successMsg.parentNode) {
              successMsg.remove();
            }
          }, 3000);
        };

        reader.onerror = function(error) {
          console.error('Resource edit form - FileReader error:', error);
          alert('Error reading file. Please try again.');
        };

        reader.readAsDataURL(file);
      } else {
        console.log('Resource edit form - No files selected');
      }
    });
    
    // Simple form validation
    const resourceForm = document.getElementById('resourceForm');
    resourceForm.addEventListener('submit', function(event) {
      const title = document.getElementById('title').value.trim();
      const category = document.getElementById('category').value;
      const summary = document.getElementById('summary').value.trim();
      const content = document.getElementById('content').value.trim();
      
      if (!title || !category || !summary || !content) {
        event.preventDefault();
        alert('Please fill in all required fields.');
      }
    });
  });
</script>

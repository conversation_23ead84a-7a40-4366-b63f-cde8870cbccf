<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Course Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="mb-0"><i class="bi bi-star-fill"></i> Premium Course Test & Debug</h3>
                    </div>
                    <div class="card-body">
                        <div id="status" class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Testing premium course functionality...
                        </div>
                        
                        <!-- Test Actions -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5><i class="bi bi-gear"></i> Admin Actions</h5>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" onclick="loginAsAdmin()">
                                        <i class="bi bi-box-arrow-in-right"></i> Login as Admin
                                    </button>
                                    <button class="btn btn-warning" onclick="makeAdmin()">
                                        <i class="bi bi-shield-plus"></i> Make Current User Admin
                                    </button>
                                    <a href="/admin/courses/new" class="btn btn-primary" target="_blank">
                                        <i class="bi bi-plus-circle"></i> Create Premium Course
                                    </a>
                                    <a href="/admin/courses" class="btn btn-outline-primary" target="_blank">
                                        <i class="bi bi-list"></i> View Admin Courses
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="bi bi-mortarboard"></i> Course Actions</h5>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-info" onclick="checkCourses()">
                                        <i class="bi bi-search"></i> Check All Courses
                                    </button>
                                    <button class="btn btn-success" onclick="createTestPremiumCourse()">
                                        <i class="bi bi-star"></i> Create Test Premium Course
                                    </button>
                                    <a href="/courses" class="btn btn-outline-info" target="_blank">
                                        <i class="bi bi-mortarboard"></i> View User Courses
                                    </a>
                                    <a href="/courses/debug" class="btn btn-outline-secondary" target="_blank">
                                        <i class="bi bi-bug"></i> Debug Courses API
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course List -->
                        <div class="mb-4">
                            <h5><i class="bi bi-list-ul"></i> Current Courses</h5>
                            <div id="coursesList" class="alert alert-light">
                                Click "Check All Courses" to load courses...
                            </div>
                        </div>
                        
                        <!-- Test Results -->
                        <div class="mb-4">
                            <h5><i class="bi bi-clipboard-data"></i> Test Results</h5>
                            <div id="testResults" class="alert alert-light">
                                No tests run yet.
                            </div>
                        </div>
                        
                        <!-- Quick Links -->
                        <div class="mt-4">
                            <h6>Quick Navigation:</h6>
                            <a href="/simple-login.html" class="btn btn-outline-primary btn-sm me-2">Login</a>
                            <a href="/dashboard" class="btn btn-outline-success btn-sm me-2">Dashboard</a>
                            <a href="/courses" class="btn btn-outline-info btn-sm me-2">Courses</a>
                            <a href="/admin" class="btn btn-outline-danger btn-sm">Admin</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getAuth, 
            signInWithEmailAndPassword,
            onAuthStateChanged
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
            authDomain: "sustainablefarming-bf265.firebaseapp.com",
            projectId: "sustainablefarming-bf265",
            storageBucket: "sustainablefarming-bf265.appspot.com",
            messagingSenderId: "89904373415",
            appId: "1:89904373415:web:2b8bbc14c7802554cac582"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        const status = document.getElementById('status');
        const coursesList = document.getElementById('coursesList');
        const testResults = document.getElementById('testResults');

        function updateStatus(message, type = 'info') {
            status.innerHTML = `<i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}`;
            status.className = `alert alert-${type}`;
        }

        function updateCoursesList(courses) {
            if (!courses || courses.length === 0) {
                coursesList.innerHTML = '<div class="text-muted">No courses found.</div>';
                return;
            }

            let html = '<div class="row">';
            courses.forEach(course => {
                const premiumBadge = course.isPremium ? 
                    `<span class="badge bg-warning text-dark"><i class="bi bi-star-fill"></i> Premium $${course.price}</span>` : 
                    '<span class="badge bg-success">Free</span>';
                
                const publishedBadge = course.isPublished ? 
                    '<span class="badge bg-success">Published</span>' : 
                    '<span class="badge bg-secondary">Draft</span>';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">${course.title}</h6>
                                <p class="card-text small">${course.description || 'No description'}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        ${premiumBadge}
                                        ${publishedBadge}
                                    </div>
                                    <small class="text-muted">${course.category || 'No category'}</small>
                                </div>
                                <div class="mt-2">
                                    <a href="/courses/${course.id}" class="btn btn-sm btn-outline-primary" target="_blank">View Course</a>
                                    ${course.isPremium ? `<a href="/courses/${course.id}/purchase" class="btn btn-sm btn-warning" target="_blank">Purchase</a>` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            coursesList.innerHTML = html;
        }

        function updateTestResults(message, type = 'light') {
            testResults.innerHTML = message;
            testResults.className = `alert alert-${type}`;
        }

        // Auth state observer
        onAuthStateChanged(auth, (user) => {
            if (user) {
                updateStatus(`Logged in as: ${user.email}`, 'success');
            } else {
                updateStatus('Not logged in. Please log in to test premium features.', 'warning');
            }
        });

        // Global functions
        window.loginAsAdmin = async function() {
            try {
                updateStatus('Logging in as test user...', 'info');
                
                const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'test123456');
                updateStatus('Login successful!', 'success');
                updateTestResults('✅ Login test passed', 'success');
            } catch (error) {
                console.error('Login error:', error);
                updateStatus(`Login failed: ${error.message}`, 'danger');
                updateTestResults('❌ Login test failed: ' + error.message, 'danger');
            }
        };

        window.makeAdmin = async function() {
            try {
                updateStatus('Making current user admin...', 'info');
                
                const response = await fetch('/make-me-admin');
                const result = await response.json();
                
                if (result.success) {
                    updateStatus('Successfully made admin!', 'success');
                    updateTestResults('✅ Admin creation test passed: ' + result.message, 'success');
                } else {
                    updateStatus('Failed to make admin: ' + result.message, 'danger');
                    updateTestResults('❌ Admin creation test failed: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('Make admin error:', error);
                updateStatus('Error making admin: ' + error.message, 'danger');
                updateTestResults('❌ Admin creation test failed: ' + error.message, 'danger');
            }
        };

        window.checkCourses = async function() {
            try {
                updateStatus('Fetching all courses...', 'info');
                
                const response = await fetch('/courses/debug');
                const result = await response.json();
                
                if (response.ok) {
                    updateStatus(`Found ${result.count} courses`, 'success');
                    updateCoursesList(result.courses);
                    
                    const premiumCourses = result.courses.filter(c => c.isPremium);
                    const publishedCourses = result.courses.filter(c => c.isPublished);
                    
                    updateTestResults(`
                        ✅ Course fetch test passed<br>
                        📊 Total courses: ${result.count}<br>
                        ⭐ Premium courses: ${premiumCourses.length}<br>
                        📢 Published courses: ${publishedCourses.length}<br>
                        📝 Draft courses: ${result.count - publishedCourses.length}
                    `, 'info');
                } else {
                    throw new Error('Failed to fetch courses');
                }
            } catch (error) {
                console.error('Check courses error:', error);
                updateStatus('Error fetching courses: ' + error.message, 'danger');
                updateTestResults('❌ Course fetch test failed: ' + error.message, 'danger');
            }
        };

        window.createTestPremiumCourse = async function() {
            try {
                updateStatus('Creating test premium course...', 'info');
                
                const courseData = {
                    title: 'Test Premium Course - ' + new Date().toLocaleTimeString(),
                    description: 'This is a test premium course created automatically for testing purposes.',
                    category: 'organic-farming',
                    level: 'intermediate',
                    duration: 120,
                    objectives: 'Learn premium farming techniques\nMaster advanced organic methods\nGet certified in sustainable practices',
                    prerequisites: 'Basic farming knowledge\nAccess to farming equipment',
                    tags: 'premium, test, organic, advanced',
                    isPremium: 'on',
                    price: 99.99,
                    currency: 'USD',
                    enrollmentLimit: 25,
                    requiresApproval: 'on',
                    certificateTemplate: 'professional',
                    isPublished: 'on'
                };

                const formData = new FormData();
                Object.keys(courseData).forEach(key => {
                    formData.append(key, courseData[key]);
                });

                const response = await fetch('/admin/courses', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    updateStatus('Test premium course created successfully!', 'success');
                    updateTestResults('✅ Premium course creation test passed', 'success');
                    
                    // Refresh course list
                    setTimeout(() => {
                        window.checkCourses();
                    }, 1000);
                } else {
                    throw new Error('Failed to create course');
                }
            } catch (error) {
                console.error('Create course error:', error);
                updateStatus('Error creating test course: ' + error.message, 'danger');
                updateTestResults('❌ Premium course creation test failed: ' + error.message, 'danger');
            }
        };

        // Initialize
        setTimeout(() => {
            updateStatus('Premium course test page loaded. Ready for testing.', 'success');
            window.checkCourses();
        }, 1000);
    </script>
</body>
</html>

<div class="container">
  <div class="row">
    <div class="col-md-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Add New Resource</h1>
        <a href="/admin/resources" class="btn btn-outline-secondary">
          <i class="bi bi-arrow-left"></i> Back to Resources
        </a>
      </div>

      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      <% } %>

      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h4 class="mb-0">Resource Details</h4>
        </div>
        <div class="card-body">
          <form action="/admin/resources" method="POST" id="resourceForm" enctype="multipart/form-data">
            <div class="row mb-3">
              <div class="col-md-8">
                <div class="mb-3">
                  <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="title" name="title" required
                         value="<%= typeof formData !== 'undefined' ? formData.title : '' %>">
                </div>

                <div class="mb-3">
                  <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                  <select class="form-select" id="category" name="category" required>
                    <option value="" disabled <%= typeof formData === 'undefined' || !formData.category ? 'selected' : '' %>>Select a category</option>
                    <option value="organic-farming" <%= typeof formData !== 'undefined' && formData.category === 'organic-farming' ? 'selected' : '' %>>Organic Farming</option>
                    <option value="water-conservation" <%= typeof formData !== 'undefined' && formData.category === 'water-conservation' ? 'selected' : '' %>>Water Conservation</option>
                    <option value="renewable-energy" <%= typeof formData !== 'undefined' && formData.category === 'renewable-energy' ? 'selected' : '' %>>Renewable Energy</option>
                    <option value="soil-health" <%= typeof formData !== 'undefined' && formData.category === 'soil-health' ? 'selected' : '' %>>Soil Health</option>
                    <option value="pest-management" <%= typeof formData !== 'undefined' && formData.category === 'pest-management' ? 'selected' : '' %>>Pest Management</option>
                    <option value="livestock" <%= typeof formData !== 'undefined' && formData.category === 'livestock' ? 'selected' : '' %>>Livestock</option>
                    <option value="other" <%= typeof formData !== 'undefined' && formData.category === 'other' ? 'selected' : '' %>>Other</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label for="summary" class="form-label">Summary <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="summary" name="summary" rows="3" required><%= typeof formData !== 'undefined' ? formData.summary : '' %></textarea>
                  <div class="form-text">A brief summary that will appear in resource listings (100-150 characters recommended).</div>
                </div>

                <div class="mb-3">
                  <label for="tags" class="form-label">Tags</label>
                  <input type="text" class="form-control" id="tags" name="tags"
                         value="<%= typeof formData !== 'undefined' ? formData.tags : '' %>">
                  <div class="form-text">Comma-separated list of tags (e.g., organic, water, soil).</div>
                </div>
              </div>

              <div class="col-md-4">
                <div class="mb-3">
                  <label class="form-label">Featured Image</label>
                  <div class="card">
                    <div class="card-body text-center">
                      <div id="imagePreview" class="mb-3 d-flex justify-content-center align-items-center bg-light" style="height: 200px; overflow: hidden;">
                        <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                      </div>
                      <input type="file" class="form-control mb-2" id="imageUpload" accept="image/*">
                      <input type="hidden" id="imageDataUrl" name="imageDataUrl">
                      <div class="form-text">Recommended size: 800x600 pixels.</div>
                    </div>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="isPublished" name="isPublished"
                           <%= typeof formData !== 'undefined' && formData.isPublished ? 'checked' : '' %>>
                    <label class="form-check-label" for="isPublished">Publish immediately</label>
                  </div>
                  <div class="form-text">If unchecked, the resource will be saved as a draft.</div>
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
              <textarea class="form-control" id="content" name="content" rows="15" required><%= typeof formData !== 'undefined' ? formData.content : '' %></textarea>
              <div class="form-text">You can use Markdown formatting for the content.</div>
            </div>

            <div class="d-flex justify-content-between">
              <a href="/admin/resources" class="btn btn-outline-secondary">Cancel</a>
              <button type="submit" class="btn btn-success">Save Resource</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Image upload preview
    const imageUpload = document.getElementById('imageUpload');
    const imagePreview = document.getElementById('imagePreview');
    const imageDataUrl = document.getElementById('imageDataUrl');

    imageUpload.addEventListener('change', function() {
      if (this.files && this.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
          // Clear the preview
          imagePreview.innerHTML = '';

          // Create image element
          const img = document.createElement('img');
          img.src = e.target.result;
          img.classList.add('img-fluid');

          // Store the data URL
          imageDataUrl.value = e.target.result;

          // Add image to preview
          imagePreview.appendChild(img);
        };

        reader.readAsDataURL(this.files[0]);
      }
    });

    // Simple form validation
    const resourceForm = document.getElementById('resourceForm');
    resourceForm.addEventListener('submit', function(event) {
      const title = document.getElementById('title').value.trim();
      const category = document.getElementById('category').value;
      const summary = document.getElementById('summary').value.trim();
      const content = document.getElementById('content').value.trim();

      if (!title || !category || !summary || !content) {
        event.preventDefault();
        alert('Please fill in all required fields.');
      }
    });
  });
</script>

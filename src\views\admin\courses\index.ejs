<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/admin">Admin Dashboard</a></li>
          <li class="breadcrumb-item active" aria-current="page">Courses</li>
        </ol>
      </nav>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-8">
      <h1>Course Management</h1>
    </div>
    <div class="col-md-4 text-end">
      <a href="/admin/courses/new" class="btn btn-success">
        <i class="bi bi-plus-circle"></i> Create New Course
      </a>
    </div>
  </div>

  <% if (typeof error !== 'undefined') { %>
    <div class="row">
      <div class="col-md-12">
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">All Courses</h5>
            <div class="input-group" style="width: 300px;">
              <input type="text" class="form-control" id="courseSearch" placeholder="Search courses...">
              <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Title</th>
                  <th>Category</th>
                  <th>Level</th>
                  <th>Type</th>
                  <th>Modules</th>
                  <th>Enrollments</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% if (courses && courses.length > 0) { %>
                  <% courses.forEach(course => { %>
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <% if (course.imageUrl) { %>
                            <img src="<%= course.imageUrl %>" alt="<%= course.title %>" class="me-2 rounded" style="width: 40px; height: 40px; object-fit: cover;">
                          <% } else { %>
                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                              <i class="bi bi-book text-muted"></i>
                            </div>
                          <% } %>
                          <div>
                            <div class="fw-bold">
                              <%= course.title %>
                              <% if (course.isPremium) { %>
                                <i class="bi bi-star-fill text-warning ms-1" title="Premium Course"></i>
                              <% } %>
                            </div>
                            <small class="text-muted">Created: <%= new Date(course.createdAt).toLocaleDateString() %></small>
                          </div>
                        </div>
                      </td>
                      <td><%= course.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></td>
                      <td><span class="badge bg-<%= course.level === 'beginner' ? 'success' : (course.level === 'intermediate' ? 'warning' : 'danger') %>">
                        <%= course.level.charAt(0).toUpperCase() + course.level.slice(1) %>
                      </span></td>
                      <td>
                        <% if (course.isPremium) { %>
                          <span class="badge bg-warning text-dark">
                            <i class="bi bi-star-fill"></i> Premium
                            <% if (course.price > 0) { %>
                              <br><small><%= course.currency %> <%= course.price %></small>
                            <% } else { %>
                              <br><small>Free</small>
                            <% } %>
                          </span>
                        <% } else { %>
                          <span class="badge bg-success">Free</span>
                        <% } %>
                      </td>
                      <td><%= course.moduleCount || 0 %></td>
                      <td>
                        <%= course.enrollmentCount || 0 %>
                        <% if (course.isPremium && course.requiresApproval) { %>
                          <br><small class="text-muted">
                            <% const pendingCount = (course.pendingEnrollments && course.pendingEnrollments.length) || 0; %>
                            <% if (pendingCount > 0) { %>
                              <span class="badge bg-warning text-dark"><%= pendingCount %> pending</span>
                            <% } %>
                          </small>
                        <% } %>
                      </td>
                      <td>
                        <div class="form-check form-switch">
                          <input class="form-check-input" type="checkbox" role="switch" id="publishSwitch-<%= course.id %>" 
                                <%= course.isPublished ? 'checked' : '' %>
                                onchange="toggleCoursePublished('<%= course.id %>', this)">
                          <label class="form-check-label" for="publishSwitch-<%= course.id %>">
                            <span class="badge bg-<%= course.isPublished ? 'success' : 'secondary' %>">
                              <%= course.isPublished ? 'Published' : 'Draft' %>
                            </span>
                          </label>
                        </div>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <a href="/courses/<%= course.id %>" class="btn btn-outline-secondary" title="View" target="_blank">
                            <i class="bi bi-eye"></i>
                          </a>
                          <a href="/admin/courses/<%= course.id %>/edit" class="btn btn-outline-primary" title="Edit">
                            <i class="bi bi-pencil"></i>
                          </a>
                          <a href="/admin/courses/<%= course.id %>/modules" class="btn btn-outline-success" title="Modules">
                            <i class="bi bi-collection"></i>
                          </a>
                          <button type="button" class="btn btn-outline-danger" title="Delete" 
                                  onclick="confirmDeleteCourse('<%= course.id %>', '<%= course.title %>')">
                            <i class="bi bi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% }); %>
                <% } else { %>
                  <tr>
                    <td colspan="8" class="text-center py-4">
                      <div class="text-muted">
                        <i class="bi bi-info-circle fs-4 mb-3 d-block"></i>
                        <p>No courses found. <a href="/admin/courses/new">Create your first course</a>.</p>
                      </div>
                    </td>
                  </tr>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Course Modal -->
<div class="modal fade" id="deleteCourseModal" tabindex="-1" aria-labelledby="deleteCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteCourseModalLabel">Confirm Deletion</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the course "<span id="courseTitle"></span>"?</p>
        <p class="text-danger">This action cannot be undone. All modules and content will be permanently deleted.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <form id="deleteCourseForm" method="POST" action="">
          <button type="submit" class="btn btn-danger">Delete Course</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  function confirmDeleteCourse(courseId, courseTitle) {
    document.getElementById('courseTitle').textContent = courseTitle;
    document.getElementById('deleteCourseForm').action = `/admin/courses/${courseId}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteCourseModal'));
    modal.show();
  }
  
  function toggleCoursePublished(courseId, checkbox) {
    const isChecked = checkbox.checked;
    const label = checkbox.nextElementSibling.querySelector('.badge');
    
    // Optimistic UI update
    label.className = `badge bg-${isChecked ? 'success' : 'secondary'}`;
    label.textContent = isChecked ? 'Published' : 'Draft';
    
    // Send request to server
    fetch(`/admin/courses/${courseId}/toggle-published`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ isPublished: isChecked })
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        // Revert UI if there was an error
        checkbox.checked = !isChecked;
        label.className = `badge bg-${!isChecked ? 'success' : 'secondary'}`;
        label.textContent = !isChecked ? 'Published' : 'Draft';
        alert('Error: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      // Revert UI on error
      checkbox.checked = !isChecked;
      label.className = `badge bg-${!isChecked ? 'success' : 'secondary'}`;
      label.textContent = !isChecked ? 'Published' : 'Draft';
      alert('An error occurred. Please try again.');
    });
  }
  
  // Simple search functionality
  document.getElementById('courseSearch').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
      const title = row.querySelector('td:first-child').textContent.toLowerCase();
      const category = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
      
      if (title.includes(searchTerm) || category.includes(searchTerm)) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  });
</script>
